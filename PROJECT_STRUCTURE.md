# 📁 项目结构说明

## 🎯 项目概述

这是一个体育赔率数据监控系统，使用浏览器自动化技术绕过跨域限制，实现稳定的WebSocket连接和数据获取。

## 📂 目录结构

```
zqSport/
├── src/main/java/com/zqsport/
│   ├── SportsDataMonitor.java          # 🌟 主应用程序（推荐）
│   ├── DetailedOddsMonitor.java        # 详细赔率监控
│   ├── AutoTokenApp.java               # 自动Token应用（已弃用）
│   ├── OddsMonitor.java                # 简单赔率监控
│   ├── SportsDataApp.java              # 交互式应用
│   └── websocket/                      # WebSocket核心组件
│       ├── BrowserTokenService.java    # 🔑 浏览器自动化服务
│       ├── SportsWebSocketManager.java # WebSocket管理器
│       ├── SportsWebSocketClient.java  # WebSocket客户端
│       ├── ReconnectManager.java       # 重连管理器
│       ├── TokenService.java           # Token服务
│       ├── MessageProcessor.java       # 消息处理接口
│       ├── WebSocketConfig.java        # 配置类
│       ├── model/                      # 数据模型
│       └── parser/                     # 数据解析器
├── src/main/resources/
│   └── logback.xml                     # 日志配置
├── target/                             # 编译输出
├── logs/                               # 日志文件
├── pom.xml                             # Maven配置
├── run.bat                             # 启动脚本
└── README.md                           # 项目说明
```

## 🚀 核心应用程序

### 1. SportsDataMonitor.java 🌟 **推荐**

**主要功能：**
- 🌐 浏览器自动化获取token
- 🔄 智能自动重连机制
- 📊 实时赔率数据监控
- 🎮 交互式命令控制

**使用场景：** 生产环境长期稳定运行

**启动方式：**
```bash
run.bat  # 选择选项 1
# 或
java -cp "target/classes;target/dependency/*" com.zqsport.SportsDataMonitor
```

### 2. DetailedOddsMonitor.java

**主要功能：**
- 📈 详细的赔率数据解析
- 🏆 比赛信息展示
- 📊 多种盘口支持
- ⏰ 时间格式化显示

**使用场景：** 需要详细数据分析时

**注意：** 需要手动提供有效token

### 3. AutoTokenApp.java ⚠️ **已弃用**

**主要功能：**
- 🔗 传统HTTP方式获取token
- 🔄 基础重连功能

**问题：** 受跨域限制影响，可能无法正常工作

**替代方案：** 使用 SportsDataMonitor

### 4. OddsMonitor.java

**主要功能：**
- 📊 基础赔率监控
- 💬 简单消息处理

**使用场景：** 简单测试和验证

## 🔧 核心组件

### WebSocket管理层

#### SportsWebSocketManager.java
- WebSocket连接管理
- 自动重连控制
- 消息订阅管理
- 状态监控

#### SportsWebSocketClient.java
- 底层WebSocket通信
- 消息接收和发送
- 连接事件处理

#### ReconnectManager.java
- 智能重连策略
- 指数退避算法
- 重连状态管理

### 认证服务层

#### BrowserTokenService.java 🔑 **核心**
- Chrome浏览器自动化
- 登录状态检测
- Token和Cookies获取
- JavaScript执行

#### TokenService.java
- 传统HTTP Token获取
- API请求处理
- 错误处理

### 数据处理层

#### MessageProcessor.java
- 消息处理接口
- 统一消息处理规范

#### model/ 目录
- 数据模型定义
- JSON映射类

#### parser/ 目录
- 数据解析器
- 格式转换工具

## 🛠️ 技术栈

### 核心依赖

| 组件 | 版本 | 用途 |
|------|------|------|
| **Selenium WebDriver** | 4.15.0 | 浏览器自动化 |
| **WebDriverManager** | 5.6.2 | 自动管理ChromeDriver |
| **Java-WebSocket** | 1.5.4 | WebSocket客户端 |
| **Jackson** | 2.15.2 | JSON处理 |
| **Logback** | 1.4.8 | 日志记录 |

### 系统要求

- **Java**: 11+
- **Chrome浏览器**: 最新版本
- **Maven**: 3.6+
- **操作系统**: Windows/Linux/macOS

## 📋 使用指南

### 快速开始

1. **编译项目**
```bash
mvn clean compile
mvn dependency:copy-dependencies
```

2. **运行主程序**
```bash
run.bat  # 选择选项 1
```

3. **操作流程**
- 程序自动打开Chrome浏览器
- 在浏览器中手动登录 pin975.com
- 程序自动检测登录并获取认证信息
- 自动建立WebSocket连接
- 开始接收实时数据

### 交互命令

| 命令 | 功能 |
|------|------|
| `status` | 查看连接状态 |
| `stats` | 查看统计信息 |
| `refresh` | 刷新token并重连 |
| `reconnect` | 手动触发重连 |
| `quit` | 退出程序 |

## 🔍 故障排除

### 常见问题

1. **浏览器启动失败**
   - 检查Chrome浏览器安装
   - 确认网络连接正常

2. **Token获取失败**
   - 确保正确登录网站
   - 检查cookies是否有效

3. **WebSocket连接失败**
   - 验证token有效性
   - 检查网络防火墙设置

### 日志查看

- **控制台输出**: 实时状态信息
- **日志文件**: `logs/sports-websocket.log`

## 🎉 项目特色

### ✅ 核心优势

1. **🔓 绕过跨域限制** - 浏览器自动化完美解决CORS问题
2. **🤖 全自动化** - 从认证到数据接收全程自动化
3. **🔄 智能重连** - 指数退避策略，稳定可靠
4. **📊 实时监控** - 详细的状态和统计信息
5. **🎮 交互控制** - 丰富的命令行交互功能

### 🚀 技术创新

- **浏览器自动化认证** - 突破传统HTTP限制
- **智能重连机制** - 自动处理网络异常
- **模块化设计** - 清晰的代码结构
- **完善的错误处理** - 稳定的异常处理机制

---

🎯 **这是一个完整、稳定、可靠的体育赔率数据监控解决方案！**
