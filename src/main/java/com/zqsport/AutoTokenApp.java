package com.zqsport;

import com.zqsport.websocket.SportsWebSocketManager;
import com.zqsport.websocket.TokenService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Scanner;

/**
 * 自动Token应用程序
 * 使用传统HTTP方式获取token（可能受跨域限制）
 *
 * @deprecated 推荐使用 SportsDataMonitor（浏览器自动化方案）
 */
@Deprecated
public class AutoTokenApp {
    
    private static final Logger logger = LoggerFactory.getLogger(AutoTokenApp.class);
    
    public static void main(String[] args) {
        // ULP parameter from your example
        String ulp = "L3gwck1lZkl5VTIxdytKOUptWG14YU1tTko5MnpDK3crbGNIYmRsNjZRcE1HcmVwRG5UeFVyd01JU1pveDMxRWJCOW5iRVFhSkxJSUZpaU5TUk52OHc9PXwzZWRjYjdmMTQ4MzMyY2ZiYjdiMzJjNzgwMTBiYjc2Ng%3D%3D";
        
        logger.info("=== 自动Token获取体育赔率监控程序 ===");
        
        SportsWebSocketManager manager = new SportsWebSocketManager();
        Scanner scanner = new Scanner(System.in);
        
        try {
            System.out.println("选择连接方式：");
            System.out.println("1. 自动获取Token（使用示例cookies）");
            System.out.println("2. 自动获取Token（输入自定义cookies）");
            System.out.println("3. 手动输入Token");
            System.out.print("请选择 (1-3): ");
            
            String choice = scanner.nextLine().trim();
            boolean connected = false;
            
            switch (choice) {
                case "1":
                    logger.info("使用示例cookies自动获取token...");
                    connected = manager.connectWithAutoToken(ulp);
                    break;
                    
                case "2":
                    System.out.print("请输入cookie字符串（从浏览器开发者工具复制）: ");
                    String cookieString = scanner.nextLine().trim();
                    
                    if (!cookieString.isEmpty()) {
                        Map<String, String> cookies = TokenService.parseCookieString(cookieString);
                        logger.info("解析到 {} 个cookie", cookies.size());
                        connected = manager.connectWithAutoToken(cookies, ulp);
                    } else {
                        logger.warn("Cookie字符串为空，使用示例cookies");
                        connected = manager.connectWithAutoToken(ulp);
                    }
                    break;
                    
                case "3":
                    System.out.print("请输入Token: ");
                    String token = scanner.nextLine().trim();
                    
                    if (!token.isEmpty()) {
                        connected = manager.connect(token, ulp);
                    } else {
                        logger.error("Token不能为空");
                        return;
                    }
                    break;
                    
                default:
                    logger.info("无效选择，使用默认方式（自动获取token）");
                    connected = manager.connectWithAutoToken(ulp);
            }
            
            if (connected) {
                logger.info("✓ 连接成功！开始监控赔率数据...");
                
                // 订阅赔率数据
                manager.subscribeToOdds();
                
                System.out.println("\n监控选项：");
                System.out.println("- 输入 'status' 查看连接状态");
                System.out.println("- 输入 'subscribe <sportId>' 订阅特定体育项目");
                System.out.println("- 输入 'reconnect' 重新连接");
                System.out.println("- 输入 'quit' 退出程序");
                System.out.println();
                
                boolean running = true;
                while (running) {
                    System.out.print("输入命令: ");
                    String command = scanner.nextLine().trim().toLowerCase();
                    
                    if (command.isEmpty()) {
                        continue;
                    }
                    
                    String[] parts = command.split("\\s+");
                    String action = parts[0];
                    
                    switch (action) {
                        case "status":
                            System.out.println("WebSocket连接状态: " + 
                                             (manager.isConnected() ? "已连接" : "未连接"));
                            break;
                            
                        case "subscribe":
                            if (parts.length > 1) {
                                try {
                                    int sportId = Integer.parseInt(parts[1]);
                                    manager.subscribeToOdds(sportId, "zh_CN");
                                    System.out.println("已订阅体育项目ID: " + sportId);
                                } catch (NumberFormatException e) {
                                    System.out.println("无效的体育项目ID: " + parts[1]);
                                }
                            } else {
                                manager.subscribeToOdds();
                                System.out.println("已订阅默认体育项目（足球）");
                            }
                            break;
                            
                        case "reconnect":
                            logger.info("重新连接...");
                            manager.disconnect();
                            Thread.sleep(2000);
                            
                            // 重新使用自动token获取
                            connected = manager.connectWithAutoToken(ulp);
                            if (connected) {
                                manager.subscribeToOdds();
                                System.out.println("重新连接成功");
                            } else {
                                System.out.println("重新连接失败");
                            }
                            break;
                            
                        case "quit":
                        case "exit":
                            running = false;
                            break;
                            
                        case "help":
                            System.out.println("可用命令：");
                            System.out.println("  status - 查看连接状态");
                            System.out.println("  subscribe [sportId] - 订阅体育项目");
                            System.out.println("  reconnect - 重新连接");
                            System.out.println("  quit/exit - 退出程序");
                            break;
                            
                        default:
                            System.out.println("未知命令: " + command + "。输入 'help' 查看帮助。");
                    }
                }
                
            } else {
                logger.error("✗ 连接失败");
                System.out.println("\n可能的原因：");
                System.out.println("1. Token获取失败（需要有效的cookies）");
                System.out.println("2. ULP参数已过期");
                System.out.println("3. 网络连接问题");
                System.out.println("4. 服务器拒绝连接");
                System.out.println("\n解决方案：");
                System.out.println("1. 从浏览器开发者工具获取最新的cookies");
                System.out.println("2. 确认已登录到网站");
                System.out.println("3. 检查网络连接");
            }
            
        } catch (Exception e) {
            logger.error("程序运行出错", e);
        } finally {
            // 清理资源
            logger.info("正在关闭程序...");
            manager.disconnect();
            scanner.close();
        }
        
        logger.info("=== 程序结束 ===");
    }
}
