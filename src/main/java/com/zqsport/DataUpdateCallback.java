package com.zqsport;

import java.util.List;

/**
 * 数据更新回调接口
 * 用于将解析后的体育数据传递给UI组件
 */
public interface DataUpdateCallback {
    
    /**
     * 更新体育赛事数据
     * @param events 体育赛事列表
     */
    void updateEvents(List<SportEvent> events);
    
    /**
     * 体育赛事数据模型
     */
    public static class SportEvent {
        private String matchId;
        private String leagueName;
        private String homeTeam;
        private String awayTeam;
        private String startTime;
        private String matchTime;
        private String score;
        private String status;
        private String handicapData;
        private String overUnderData;
        private String firstHalfData;
        
        public SportEvent(String matchId, String leagueName, String homeTeam, String awayTeam,
                         String startTime, String matchTime, String score, String status,
                         String handicapData, String overUnderData, String firstHalfData) {
            this.matchId = matchId;
            this.leagueName = leagueName;
            this.homeTeam = homeTeam;
            this.awayTeam = awayTeam;
            this.startTime = startTime;
            this.matchTime = matchTime;
            this.score = score;
            this.status = status;
            this.handicapData = handicapData;
            this.overUnderData = overUnderData;
            this.firstHalfData = firstHalfData;
        }
        
        // Getters
        public String getMatchId() { return matchId; }
        public String getLeagueName() { return leagueName; }
        public String getHomeTeam() { return homeTeam; }
        public String getAwayTeam() { return awayTeam; }
        public String getStartTime() { return startTime; }
        public String getMatchTime() { return matchTime; }
        public String getScore() { return score; }
        public String getStatus() { return status; }
        public String getHandicapData() { return handicapData; }
        public String getOverUnderData() { return overUnderData; }
        public String getFirstHalfData() { return firstHalfData; }
        
        // Setters
        public void setMatchId(String matchId) { this.matchId = matchId; }
        public void setLeagueName(String leagueName) { this.leagueName = leagueName; }
        public void setHomeTeam(String homeTeam) { this.homeTeam = homeTeam; }
        public void setAwayTeam(String awayTeam) { this.awayTeam = awayTeam; }
        public void setStartTime(String startTime) { this.startTime = startTime; }
        public void setMatchTime(String matchTime) { this.matchTime = matchTime; }
        public void setScore(String score) { this.score = score; }
        public void setStatus(String status) { this.status = status; }
        public void setHandicapData(String handicapData) { this.handicapData = handicapData; }
        public void setOverUnderData(String overUnderData) { this.overUnderData = overUnderData; }
        public void setFirstHalfData(String firstHalfData) { this.firstHalfData = firstHalfData; }
        
        @Override
        public String toString() {
            return String.format("SportEvent{matchId='%s', league='%s', %s vs %s, time='%s', score='%s', status='%s'}",
                    matchId, leagueName, homeTeam, awayTeam, startTime, score, status);
        }
    }
}
