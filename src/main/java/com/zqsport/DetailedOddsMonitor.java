package com.zqsport;

import com.zqsport.websocket.MessageProcessor;
import com.zqsport.websocket.SportsWebSocketManager;
import com.zqsport.websocket.model.*;
import com.zqsport.websocket.parser.OddsDataParser;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.List;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 详细赔率监控程序
 * 提供详细的赔率数据解析和展示
 *
 * 注意：需要手动提供有效的token
 */
public class DetailedOddsMonitor extends SportsWebSocketManager implements MessageProcessor {
    
    private static final Logger logger = LoggerFactory.getLogger(DetailedOddsMonitor.class);
    private final OddsDataParser parser = new OddsDataParser();
    private final AtomicInteger messageCount = new AtomicInteger(0);
    private final AtomicInteger matchCount = new AtomicInteger(0);
    
    @Override
    protected void handleOddsUpdate(OddsData oddsData) {
        // 这个方法不再使用，我们直接处理原始消息
    }
    
    /**
     * 处理原始的UPDATE_ODDS消息
     */
    @Override
    public void processUpdateOddsMessage(String jsonMessage) {
        try {
            int msgCount = messageCount.incrementAndGet();
            
            // 解析赔率数据
            List<MatchInfo> matches = parser.parseUpdateOddsMessage(jsonMessage);
            
            if (!matches.isEmpty()) {
                matchCount.addAndGet(matches.size());
                
                System.out.println("\n" + "=".repeat(80));
                System.out.println(String.format("📊 赔率更新 #%d - 发现 %d 场比赛", msgCount, matches.size()));
                System.out.println("=".repeat(80));
                
                for (MatchInfo match : matches) {
                    displayMatchDetails(match);
                    System.out.println("-".repeat(80));
                }
                
                System.out.println(String.format("📈 统计: 已处理 %d 条消息, 共 %d 场比赛", msgCount, matchCount.get()));
                System.out.println("=".repeat(80));
            }
            
        } catch (Exception e) {
            logger.error("处理详细赔率消息失败", e);
        }
    }
    
    /**
     * 显示比赛详细信息
     */
    private void displayMatchDetails(MatchInfo match) {
        System.out.println();
        
        // 比赛基本信息
        System.out.println("🏆 " + match.toString());
        
        // 显示胜负盘
        if (match.getWinDrawWinMarket() != null) {
            System.out.println("   " + match.getWinDrawWinMarket().toString());
        }
        
        // 显示让球盘口
        if (match.getHandicapMarkets() != null && !match.getHandicapMarkets().isEmpty()) {
            System.out.println("   📈 让球盘口:");
            for (HandicapMarket handicap : match.getHandicapMarkets()) {
                System.out.println("      " + handicap.toString());
            }
        }
        
        // 显示大小球盘口
        if (match.getTotalMarkets() != null && !match.getTotalMarkets().isEmpty()) {
            System.out.println("   🎯 大小球盘口:");
            for (TotalMarket total : match.getTotalMarkets()) {
                System.out.println("      " + total.toString());
            }
        }
    }
    
    /**
     * 显示统计信息
     */
    public void printDetailedStatistics() {
        System.out.println("\n" + "=".repeat(60));
        System.out.println("📊 详细统计信息");
        System.out.println("=".repeat(60));
        System.out.println(String.format("总消息数: %d", messageCount.get()));
        System.out.println(String.format("比赛总数: %d", matchCount.get()));
        System.out.println(String.format("平均每条消息包含比赛数: %.1f", 
                                       matchCount.get() > 0 ? (double) matchCount.get() / messageCount.get() : 0));
        System.out.println("=".repeat(60));
    }
    
    public static void main(String[] args) {
        // ULP parameter from your example
        String ulp = "L3gwck1lZkl5VTIxdytKOUptWG14YU1tTko5MnpDK3crbGNIYmRsNjZRcE1HcmVwRG5UeFVyd01JU1pveDMxRWJCOW5iRVFhSkxJSUZpaU5TUk52OHc9PXwzZWRjYjdmMTQ4MzMyY2ZiYjdiMzJjNzgwMTBiYjc2Ng%3D%3D";
        
        System.out.println("🚀 启动详细赔率监控程序");
        System.out.println("📋 功能特性:");
        System.out.println("   ✅ 自动获取最新Token");
        System.out.println("   ✅ 时间戳转换为可读时间");
        System.out.println("   ✅ 清晰显示比分和比赛状态");
        System.out.println("   ✅ 多个让球盘口展示");
        System.out.println("   ✅ 多个大小球盘口展示");
        System.out.println("   ✅ 胜负盘赔率分析");
        System.out.println();
        
        DetailedOddsMonitor monitor = new DetailedOddsMonitor();

        try {
            // 设置消息处理器为自己
            monitor.setMessageProcessor(monitor);

            logger.info("🔗 连接到WebSocket...");
            boolean connected = monitor.connectWithAutoToken(ulp);
            
            if (connected) {
                logger.info("✅ 连接成功！开始监控详细赔率数据...");
                
                // 订阅赔率数据
                monitor.subscribeToOdds();
                
                System.out.println("⏱️  监控60秒，实时显示详细赔率信息...");
                System.out.println("💡 提示: 数据将自动解析并格式化显示");
                System.out.println();
                
                // 监控60秒
                for (int i = 0; i < 6; i++) {
                    Thread.sleep(10000);
                    monitor.printDetailedStatistics();
                }
                
                logger.info("⏹️  监控结束，断开连接...");
                monitor.disconnect();
                
                // 最终统计
                monitor.printDetailedStatistics();
                
                System.out.println("\n🎉 详细赔率监控完成！");
                System.out.println("📝 说明:");
                System.out.println("   - 时间戳已转换为本地时间格式");
                System.out.println("   - 比分实时更新显示");
                System.out.println("   - 让球盘口支持多个盘口同时显示");
                System.out.println("   - 大小球盘口包含风险等级分析");
                System.out.println("   - 胜负盘包含热门选项和返还率计算");
                
            } else {
                logger.error("❌ 连接失败");
                System.out.println("\n🔧 故障排除:");
                System.out.println("1. 确保已在浏览器中登录 pin975.com");
                System.out.println("2. 检查网络连接");
                System.out.println("3. 尝试获取最新的cookies");
            }
            
        } catch (InterruptedException e) {
            logger.info("程序被中断");
            monitor.disconnect();
        } catch (Exception e) {
            logger.error("程序运行出错", e);
        }
        
        System.out.println("\n👋 程序结束");
    }
}
