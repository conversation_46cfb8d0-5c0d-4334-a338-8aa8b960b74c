package com.zqsport;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqsport.websocket.SportsWebSocketManager;
import com.zqsport.websocket.model.OddsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * Enhanced odds monitoring application with detailed data processing
 */
public class OddsMonitor extends SportsWebSocketManager {
    
    private static final Logger logger = LoggerFactory.getLogger(OddsMonitor.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final AtomicInteger messageCount = new AtomicInteger(0);
    private final AtomicInteger oddsUpdateCount = new AtomicInteger(0);
    
    @Override
    protected void handleOddsUpdate(OddsData oddsData) {
        int count = messageCount.incrementAndGet();
        
        if (count % 10 == 0) { // 每10条消息显示一次统计
            logger.info("已接收 {} 条消息，其中 {} 条赔率更新", count, oddsUpdateCount.get());
        }
        
        // 处理具体的赔率数据
        if (oddsData.getBody() != null) {
            OddsData.OddsBody body = oddsData.getBody();
            logger.debug("赔率更新 - 体育项目: {}, 市场类型: {}, 赔率类型: {}", 
                        body.getSportId(), body.getMarketType(), body.getOddsType());
        }
    }
    
    /**
     * Process UPDATE_ODDS messages specifically
     */
    @SuppressWarnings("unchecked")
    public void processUpdateOddsMessage(String jsonMessage) {
        try {
            Map<String, Object> message = objectMapper.readValue(jsonMessage, Map.class);
            String type = (String) message.get("type");
            
            if ("UPDATE_ODDS".equals(type)) {
                oddsUpdateCount.incrementAndGet();
                
                String eventId = (String) message.get("id");
                Object body = message.get("body");
                
                logger.debug("赔率更新事件 ID: {}", eventId);
                
                if (body instanceof Map) {
                    Map<String, Object> bodyMap = (Map<String, Object>) body;
                    
                    // 提取关键信息
                    Object odds = bodyMap.get("odds");
                    Object matches = bodyMap.get("matches");
                    Object events = bodyMap.get("events");
                    
                    if (odds != null) {
                        logger.debug("包含赔率数据");
                    }
                    if (matches != null) {
                        logger.debug("包含比赛数据");
                    }
                    if (events != null) {
                        logger.debug("包含事件数据");
                    }
                }
            }
            
        } catch (Exception e) {
            logger.error("处理UPDATE_ODDS消息时出错: {}", jsonMessage, e);
        }
    }
    
    public void printStatistics() {
        logger.info("=== 统计信息 ===");
        logger.info("总消息数: {}", messageCount.get());
        logger.info("赔率更新数: {}", oddsUpdateCount.get());
        logger.info("===============");
    }
    
    public static void main(String[] args) {
        // 使用您提供的token和ulp参数
        String token = "AAAAAAOm1MAAAAGYRvNcW4xZ-OjGVb0OWBYaIhUvIo8Gvs5lFtYNiyqKduxUnElU";
        String ulp = "L3gwck1lZkl5VTIxdytKOUptWG14YU1tTko5MnpDK3crbGNIYmRsNjZRcE1HcmVwRG5UeFVyd01JU1pveDMxRWJCOW5iRVFhSkxJSUZpaU5TUk52OHc9PXwzZWRjYjdmMTQ4MzMyY2ZiYjdiMzJjNzgwMTBiYjc2Ng%3D%3D";
        
        logger.info("=== 体育赔率监控程序 ===");
        
        OddsMonitor monitor = new OddsMonitor();
        
        try {
            logger.info("连接到WebSocket...");
            boolean connected = monitor.connect(token, ulp);
            
            if (connected) {
                logger.info("✓ 连接成功！开始监控赔率数据...");
                
                // 订阅赔率数据
                monitor.subscribeToOdds();
                
                // 监控60秒
                logger.info("监控60秒，按Ctrl+C可提前结束...");
                
                // 每10秒显示一次统计
                for (int i = 0; i < 6; i++) {
                    Thread.sleep(10000);
                    monitor.printStatistics();
                }
                
                logger.info("监控结束，断开连接...");
                monitor.disconnect();
                
                // 最终统计
                monitor.printStatistics();
                
            } else {
                logger.error("✗ 连接失败");
                logger.info("请检查：");
                logger.info("1. Token和ULP参数是否有效");
                logger.info("2. 网络连接是否正常");
                logger.info("3. 目标网站是否可访问");
            }
            
        } catch (InterruptedException e) {
            logger.info("程序被中断");
            monitor.disconnect();
        } catch (Exception e) {
            logger.error("程序运行出错", e);
        }
        
        logger.info("=== 程序结束 ===");
    }
}
