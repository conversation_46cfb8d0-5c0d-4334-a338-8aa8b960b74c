package com.zqsport;

import com.zqsport.websocket.SportsWebSocketManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Scanner;

/**
 * Main application for connecting to sports WebSocket and receiving odds data
 */
public class SportsDataApp {
    
    private static final Logger logger = LoggerFactory.getLogger(SportsDataApp.class);
    
    public static void main(String[] args) {
        // Token and ULP parameters from your example
        String token = "AAAAAAOm1MAAAAGYRvNcW4xZ-OjGVb0OWBYaIhUvIo8Gvs5lFtYNiyqKduxUnElU";
        String ulp = "L3gwck1lZkl5VTIxdytKOUptWG14YU1tTko5MnpDK3crbGNIYmRsNjZRcE1HcmVwRG5UeFVyd01JU1pveDMxRWJCOW5iRVFhSkxJSUZpaU5TUk52OHc9PXwzZWRjYjdmMTQ4MzMyY2ZiYjdiMzJjNzgwMTBiYjc2Ng%3D%3D";
        
        SportsWebSocketManager manager = new SportsWebSocketManager();
        
        try {
            logger.info("Starting Sports Data Application...");
            
            // Connect to WebSocket
            boolean connected = manager.connect(token, ulp);
            
            if (!connected) {
                logger.error("Failed to connect to WebSocket. Exiting...");
                return;
            }
            
            // Subscribe to odds data
            logger.info("Subscribing to odds data...");
            manager.subscribeToOdds();
            
            // Keep the application running and allow user interaction
            Scanner scanner = new Scanner(System.in);
            boolean running = true;
            
            logger.info("Application is running. Type 'help' for commands or 'quit' to exit.");
            
            while (running) {
                System.out.print("Enter command: ");
                String command = scanner.nextLine().trim().toLowerCase();
                
                switch (command) {
                    case "help":
                        printHelp();
                        break;
                        
                    case "status":
                        System.out.println("WebSocket connected: " + manager.isConnected());
                        break;
                        
                    case "subscribe":
                        System.out.print("Enter sport ID (default 29): ");
                        String sportIdInput = scanner.nextLine().trim();
                        int sportId = sportIdInput.isEmpty() ? 29 : Integer.parseInt(sportIdInput);
                        
                        System.out.print("Enter locale (default zh_CN): ");
                        String locale = scanner.nextLine().trim();
                        if (locale.isEmpty()) locale = "zh_CN";
                        
                        manager.subscribeToOdds(sportId, locale);
                        System.out.println("Subscription sent for sport ID: " + sportId + ", locale: " + locale);
                        break;
                        
                    case "reconnect":
                        logger.info("Reconnecting...");
                        manager.disconnect();
                        Thread.sleep(1000);
                        connected = manager.connect(token, ulp);
                        if (connected) {
                            manager.subscribeToOdds();
                            System.out.println("Reconnected successfully");
                        } else {
                            System.out.println("Reconnection failed");
                        }
                        break;
                        
                    case "quit":
                    case "exit":
                        running = false;
                        break;
                        
                    default:
                        System.out.println("Unknown command: " + command + ". Type 'help' for available commands.");
                }
            }
            
        } catch (Exception e) {
            logger.error("Error in main application", e);
        } finally {
            // Clean up
            logger.info("Shutting down application...");
            manager.disconnect();
        }
    }
    
    private static void printHelp() {
        System.out.println("\nAvailable commands:");
        System.out.println("  help       - Show this help message");
        System.out.println("  status     - Show WebSocket connection status");
        System.out.println("  subscribe  - Subscribe to odds data with custom parameters");
        System.out.println("  reconnect  - Reconnect to WebSocket");
        System.out.println("  quit/exit  - Exit the application");
        System.out.println();
    }
}
