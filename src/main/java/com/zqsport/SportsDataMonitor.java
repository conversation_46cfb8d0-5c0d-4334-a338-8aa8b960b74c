package com.zqsport;

import com.zqsport.websocket.BrowserTokenService;
import com.zqsport.websocket.MessageProcessor;
import com.zqsport.websocket.SportsWebSocketManager;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.Map;
import java.util.Scanner;
import java.util.concurrent.atomic.AtomicInteger;

/**
 * 体育数据监控主应用程序
 *
 * 功能特性：
 * - 浏览器自动化获取token，绕过跨域限制
 * - 智能自动重连机制
 * - 实时赔率数据监控
 * - 交互式命令控制
 *
 * <AUTHOR> Team
 * @version 1.0.0
 */
public class SportsDataMonitor implements MessageProcessor {

    private static final Logger logger = LoggerFactory.getLogger(SportsDataMonitor.class);
    private final AtomicInteger messageCount = new AtomicInteger(0);
    private final AtomicInteger reconnectCount = new AtomicInteger(0);

    private BrowserTokenService browserService;
    private SportsWebSocketManager wsManager;
    private Map<String, String> cookies;
    private String ulp;

    // UI回调接口
    private DataUpdateCallback uiCallback;

    /**
     * 设置UI回调
     */
    public void setUICallback(DataUpdateCallback callback) {
        this.uiCallback = callback;
    }

    @Override
    public void processUpdateOddsMessage(String jsonMessage) {
        int count = messageCount.incrementAndGet();

        try {
            // 解析并显示比赛信息
            com.fasterxml.jackson.databind.ObjectMapper mapper = new com.fasterxml.jackson.databind.ObjectMapper();
            com.fasterxml.jackson.databind.JsonNode rootNode = mapper.readTree(jsonMessage);

            String messageType = rootNode.has("type") ? rootNode.get("type").asText() : "UNKNOWN";

            if ("FULL_ODDS".equals(messageType)) {
                System.out.println(String.format("\n📊 [%d] 接收到全量赔率数据", count));
                parseFullOddsData(rootNode);
            } else if ("UPDATE_ODDS".equals(messageType)) {
                System.out.println(String.format("🔄 [%d] 赔率更新", count));
                parseUpdateOddsData(rootNode);
            } else {
                System.out.println(String.format("📨 [%d] 接收到 %s 类型数据", count, messageType));
            }

        } catch (Exception e) {
            // 解析失败时显示基本信息和错误
            System.out.println(String.format("📊 [%d] 接收到数据更新 (解析失败: %s)", count, e.getMessage()));

            // 显示原始数据的前100个字符用于调试
            if (jsonMessage.length() > 100) {
                System.out.println("原始数据: " + jsonMessage.substring(0, 100) + "...");
            } else {
                System.out.println("原始数据: " + jsonMessage);
            }
        }
    }

    @Override
    public void processPingMessage(String jsonMessage) {
        // 心跳消息，保持静默
    }

    /**
     * 解析全量赔率数据
     */
    private void parseFullOddsData(com.fasterxml.jackson.databind.JsonNode rootNode) {
        try {
            java.util.List<DataUpdateCallback.SportEvent> events = new java.util.ArrayList<>();

            if (rootNode.has("odds") && rootNode.get("odds").has("l")) {
                com.fasterxml.jackson.databind.JsonNode leagues = rootNode.get("odds").get("l");

                for (com.fasterxml.jackson.databind.JsonNode league : leagues) {
                    if (league.isArray() && league.size() >= 3) {
                        int sportId = league.get(0).asInt();
                        String sportName = league.get(1).asText();
                        com.fasterxml.jackson.databind.JsonNode leagueData = league.get(2);

                        System.out.println(String.format("🏆 体育类型: %s (ID: %d)", sportName, sportId));

                        if (leagueData.isArray()) {
                            for (com.fasterxml.jackson.databind.JsonNode leagueInfo : leagueData) {
                                parseLeagueDataForUI(leagueInfo, events);
                            }
                        }
                    }
                }
            }

            // 将数据传递给UI
            if (uiCallback != null && !events.isEmpty()) {
                uiCallback.updateEvents(events);
            }

        } catch (Exception e) {
            System.out.println("解析全量赔率数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析联赛数据（用于UI）
     */
    private void parseLeagueDataForUI(com.fasterxml.jackson.databind.JsonNode leagueInfo,
                                     java.util.List<DataUpdateCallback.SportEvent> events) {
        try {
            if (leagueInfo.isArray() && leagueInfo.size() >= 3) {
                int leagueId = leagueInfo.get(0).asInt();
                String leagueName = leagueInfo.get(1).asText();
                com.fasterxml.jackson.databind.JsonNode matches = leagueInfo.get(2);

                System.out.println(String.format("  📋 联赛: %s (ID: %d)", leagueName, leagueId));

                if (matches.isArray()) {
                    for (com.fasterxml.jackson.databind.JsonNode match : matches) {
                        DataUpdateCallback.SportEvent event = parseMatchDataForUI(match, leagueName);
                        if (event != null) {
                            events.add(event);
                        }
                        // 同时保留控制台输出
                        parseMatchData(match);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("    解析联赛数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析联赛数据（原版本，保留控制台输出）
     */
    private void parseLeagueData(com.fasterxml.jackson.databind.JsonNode leagueInfo) {
        try {
            if (leagueInfo.isArray() && leagueInfo.size() >= 3) {
                int leagueId = leagueInfo.get(0).asInt();
                String leagueName = leagueInfo.get(1).asText();
                com.fasterxml.jackson.databind.JsonNode matches = leagueInfo.get(2);

                System.out.println(String.format("  📋 联赛: %s (ID: %d)", leagueName, leagueId));

                if (matches.isArray()) {
                    for (com.fasterxml.jackson.databind.JsonNode match : matches) {
                        parseMatchData(match);
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("    解析联赛数据失败: " + e.getMessage());
        }
    }

    /**
     * 解析比赛数据（用于UI）
     */
    private DataUpdateCallback.SportEvent parseMatchDataForUI(com.fasterxml.jackson.databind.JsonNode match, String leagueName) {
        try {
            if (match.isArray() && match.size() >= 4) {
                long matchId = match.get(0).asLong();
                String homeTeam = match.get(1).asText();
                String awayTeam = match.get(2).asText();
                int status = match.get(3).asInt();

                String statusText = getMatchStatus(status);
                String startTime = "";
                String matchTime = "";
                String score = "";

                // 解析开始时间
                if (match.size() > 4) {
                    long startTimeMs = match.get(4).asLong();
                    java.time.Instant instant = java.time.Instant.ofEpochMilli(startTimeMs);
                    java.time.LocalDateTime dateTime = java.time.LocalDateTime.ofInstant(instant, java.time.ZoneId.systemDefault());
                    startTime = dateTime.format(java.time.format.DateTimeFormatter.ofPattern("MM-dd HH:mm"));
                }

                // 解析比赛时间和比分
                if (match.size() > 15) {
                    matchTime = match.get(15).asText("") + " " + match.get(16).asText("");

                    if (match.size() > 9) {
                        com.fasterxml.jackson.databind.JsonNode scoreNode = match.get(9);
                        if (scoreNode.isArray() && scoreNode.size() >= 2) {
                            int homeScore = scoreNode.get(0).asInt();
                            int awayScore = scoreNode.get(1).asInt();
                            score = homeScore + " - " + awayScore;
                        }
                    }
                }

                // 解析赔率数据
                String handicapData = "";
                String overUnderData = "";
                String firstHalfData = "";

                if (match.size() > 8) {
                    com.fasterxml.jackson.databind.JsonNode oddsNode = match.get(8);
                    if (oddsNode != null && oddsNode.isObject()) {
                        // 解析全场赔率数据 - 获取所有盘口
                        if (oddsNode.has("0")) {
                            com.fasterxml.jackson.databind.JsonNode fullTimeNode = oddsNode.get("0");
                            if (fullTimeNode.isArray() && fullTimeNode.size() > 0) {

                                // 解析全场让球盘 - 获取所有盘口
                                com.fasterxml.jackson.databind.JsonNode handicapNode = fullTimeNode.get(0);
                                if (handicapNode.isArray() && handicapNode.size() > 0) {
                                    StringBuilder handicapBuilder = new StringBuilder();
                                    for (com.fasterxml.jackson.databind.JsonNode handicapItem : handicapNode) {
                                        if (handicapItem.isArray() && handicapItem.size() >= 5) {
                                            String handicap = handicapItem.get(2).asText();
                                            String homeOdds = handicapItem.get(3).asText();
                                            String awayOdds = handicapItem.get(4).asText();
                                            if (handicapBuilder.length() > 0) {
                                                handicapBuilder.append("\n");
                                            }
                                            handicapBuilder.append(handicap).append(" (").append(homeOdds).append("/").append(awayOdds).append(")");
                                        }
                                    }
                                    handicapData = handicapBuilder.toString();
                                }

                                // 解析全场大小球 - 获取所有盘口
                                if (fullTimeNode.size() > 1) {
                                    com.fasterxml.jackson.databind.JsonNode ouNode = fullTimeNode.get(1);
                                    if (ouNode.isArray() && ouNode.size() > 0) {
                                        StringBuilder ouBuilder = new StringBuilder();
                                        for (com.fasterxml.jackson.databind.JsonNode ouItem : ouNode) {
                                            if (ouItem.isArray() && ouItem.size() >= 4) {
                                                String total = ouItem.get(0).asText();
                                                String overOdds = ouItem.get(2).asText();
                                                String underOdds = ouItem.get(3).asText();
                                                if (ouBuilder.length() > 0) {
                                                    ouBuilder.append("\n");
                                                }
                                                ouBuilder.append(total).append(" (").append(overOdds).append("/").append(underOdds).append(")");
                                            }
                                        }
                                        overUnderData = ouBuilder.toString();
                                    }
                                }
                            }
                        }

                        // 解析上半场数据 - 获取所有盘口
                        if (oddsNode.has("1")) {
                            com.fasterxml.jackson.databind.JsonNode firstHalfNode = oddsNode.get("1");
                            if (firstHalfNode.isArray() && firstHalfNode.size() > 0) {
                                StringBuilder firstHalfBuilder = new StringBuilder();

                                // 上半场让球盘
                                if (firstHalfNode.size() > 0) {
                                    com.fasterxml.jackson.databind.JsonNode fhHandicapNode = firstHalfNode.get(0);
                                    if (fhHandicapNode.isArray() && fhHandicapNode.size() > 0) {
                                        StringBuilder fhHandicapBuilder = new StringBuilder();
                                        for (com.fasterxml.jackson.databind.JsonNode handicapItem : fhHandicapNode) {
                                            if (handicapItem.isArray() && handicapItem.size() >= 5) {
                                                String handicap = handicapItem.get(2).asText();
                                                String homeOdds = handicapItem.get(3).asText();
                                                String awayOdds = handicapItem.get(4).asText();
                                                if (fhHandicapBuilder.length() > 0) {
                                                    fhHandicapBuilder.append("\n");
                                                }
                                                fhHandicapBuilder.append(handicap).append(" (").append(homeOdds).append("/").append(awayOdds).append(")");
                                            }
                                        }
                                        firstHalfBuilder.append(fhHandicapBuilder.toString());
                                    }
                                }

                                // 上半场大小球
                                if (firstHalfNode.size() > 1) {
                                    com.fasterxml.jackson.databind.JsonNode fhOuNode = firstHalfNode.get(1);
                                    if (fhOuNode.isArray() && fhOuNode.size() > 0) {
                                        StringBuilder fhOuBuilder = new StringBuilder();
                                        for (com.fasterxml.jackson.databind.JsonNode ouItem : fhOuNode) {
                                            if (ouItem.isArray() && ouItem.size() >= 4) {
                                                String total = ouItem.get(0).asText();
                                                String overOdds = ouItem.get(2).asText();
                                                String underOdds = ouItem.get(3).asText();
                                                if (fhOuBuilder.length() > 0) {
                                                    fhOuBuilder.append("\n");
                                                }
                                                fhOuBuilder.append(total).append(" (").append(overOdds).append("/").append(underOdds).append(")");
                                            }
                                        }
                                        if (firstHalfBuilder.length() > 0) {
                                            firstHalfBuilder.append("|");
                                        }
                                        firstHalfBuilder.append(fhOuBuilder.toString());
                                    }
                                }

                                firstHalfData = firstHalfBuilder.toString();
                            }
                        }
                    }
                }

                return new DataUpdateCallback.SportEvent(
                    String.valueOf(matchId),
                    leagueName,
                    homeTeam,
                    awayTeam,
                    startTime,
                    matchTime.trim(),
                    score,
                    statusText,
                    handicapData,
                    overUnderData,
                    firstHalfData
                );
            }
        } catch (Exception e) {
            System.out.println("      解析比赛数据失败 (UI): " + e.getMessage());
        }
        return null;
    }

    /**
     * 解析比赛数据
     */
    private void parseMatchData(com.fasterxml.jackson.databind.JsonNode match) {
        try {
            if (match.isArray() && match.size() >= 4) {
                long matchId = match.get(0).asLong();
                String homeTeam = match.get(1).asText();
                String awayTeam = match.get(2).asText();
                int status = match.get(3).asInt();

                String statusText = getMatchStatus(status);
                System.out.println(String.format("    ⚽ %s vs %s [%s] (ID: %d)",
                        homeTeam, awayTeam, statusText, matchId));

                if (match.size() > 4) {
                    long startTime = match.get(4).asLong();
                    java.time.Instant instant = java.time.Instant.ofEpochMilli(startTime);
                    java.time.LocalDateTime dateTime = java.time.LocalDateTime.ofInstant(instant, java.time.ZoneId.systemDefault());
                    System.out.println(String.format("      🕐 开始时间: %s", dateTime.format(java.time.format.DateTimeFormatter.ofPattern("MM-dd HH:mm"))));
                }

                if (match.size() > 15) {
                    parseMatchLiveInfo(match);
                }

                if (match.size() > 8) {
                    System.out.println("      解析赔率数据 (比赛ID: " + matchId + ")");
                    parseOddsData(match.get(8), matchId);
                } else {
                    System.out.println("      比赛ID: " + matchId + " 缺少赔率数据");
                }
            } else {
                System.out.println("      比赛数据格式错误: " + match.toString());
            }
        } catch (Exception e) {
            System.out.println("      解析比赛数据失败 (比赛ID: " + (match.has(0) ? match.get(0).asText() : "未知") + "): " + e.getMessage());
        }
    }

    /**
     * 解析比赛实时信息（比分、时间等）
     */
    private void parseMatchLiveInfo(com.fasterxml.jackson.databind.JsonNode match) {
        try {
            // 根据数据结构解析比分和比赛时间
            if (match.size() > 15) {
                String matchTime = match.get(15).asText(""); // 比赛时间，如 "15'"
                String period = match.get(16).asText(""); // 比赛阶段，如 "1H"

                if (!matchTime.isEmpty() || !period.isEmpty()) {
                    System.out.println(String.format("      ⏱️  比赛时间: %s %s", matchTime, period));
                }

                // 解析比分
                if (match.size() > 9) {
                    com.fasterxml.jackson.databind.JsonNode scoreNode = match.get(9);
                    if (scoreNode.isArray() && scoreNode.size() >= 2) {
                        int homeScore = scoreNode.get(0).asInt();
                        int awayScore = scoreNode.get(1).asInt();
                        System.out.println(String.format("      ⚽ 比分: %d - %d", homeScore, awayScore));
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("      解析比赛实时信息失败: " + e.getMessage());
        }
    }

   /**
 * 解析赔率数据
 */
private void parseOddsData(com.fasterxml.jackson.databind.JsonNode oddsNode, long matchId) {
    try {
        if (oddsNode == null || oddsNode.isNull()) {
            System.out.println("        赔率数据为null或缺失 (比赛ID: " + matchId + ")");
            return;
        }
        if (!oddsNode.isObject()) {
            System.out.println("        赔率数据不是对象类型: " + oddsNode.getNodeType() + " (比赛ID: " + matchId + ")");
            return;
        }
        if (!oddsNode.has("0") && !oddsNode.has("1")) {
            System.out.println("        赔率数据缺少全场(0)或上半场(1)键 (比赛ID: " + matchId + ")");
            return;
        }

        System.out.println("      📊 赔率信息 (比赛ID: " + matchId + "):");
        // 打印原始赔率数据（截断到500字符以便调试）
        System.out.println("        原始赔率结构: " + oddsNode.toString().substring(0, Math.min(500, oddsNode.toString().length())) + "...");
        java.util.List<String> keys = new java.util.ArrayList<>();
        oddsNode.fieldNames().forEachRemaining(keys::add);
        System.out.println("        可用的键: " + keys);

        for (String key : keys) {
            com.fasterxml.jackson.databind.JsonNode marketNode = oddsNode.get(key);
            if (marketNode != null && marketNode.isArray() && marketNode.size() > 0) {
                parseMarketByType(key, marketNode, matchId);
            } else {
                System.out.println("        盘口类型 " + key + " 数据为空或格式错误 (比赛ID: " + matchId + ")");
            }
        }
    } catch (Exception e) {
        System.out.println("        解析赔率数据失败 (比赛ID: " + matchId + "): " + e.getMessage());
        e.printStackTrace();
    }
}

/**
 * 根据盘口类型解析不同的市场数据
 */
private void parseMarketByType(String marketType, com.fasterxml.jackson.databind.JsonNode marketNode, long matchId) {
    try {
        switch (marketType) {
            case "0":   // 全场市场 [8][0]
                System.out.println("        🎯 全场市场 (类型: " + marketType + ", 比赛ID: " + matchId + "):");
                parseFullTimeMarkets(marketNode, matchId);
                break;
            case "1":   // 上半场市场 [8][1]
                System.out.println("        ⏰ 上半场市场 (类型: " + marketType + ", 比赛ID: " + matchId + "):");
                parseHalfTimeMarkets(marketNode, matchId);
                break;
            default:
                System.out.println("        ⏭️ 跳过盘口类型: " + marketType + " (非核心盘口, 比赛ID: " + matchId + ")");
                break;
        }
    } catch (Exception e) {
        System.out.println("          解析市场类型 " + marketType + " 失败 (比赛ID: " + matchId + "): " + e.getMessage());
    }
}

    /**
     * 解析全场市场数据 [8][0]
     */
    private void parseFullTimeMarkets(com.fasterxml.jackson.databind.JsonNode fullTimeNode, long matchId) {
        try {
            if (!fullTimeNode.isArray() || fullTimeNode.size() == 0) {
                System.out.println("          全场市场数据为空或非数组 (比赛ID: " + matchId + ")");
                return;
            }

            // 让球盘口
            if (fullTimeNode.size() > 0 && fullTimeNode.get(0).isArray()) {
                System.out.println("          🎯 全场让球盘口:");
                parseHandicapOdds(fullTimeNode.get(0), "全场让球", matchId);
            } else {
                System.out.println("          全场让球盘口数据缺失 (比赛ID: " + matchId + ")");
            }

            // 大小球盘口
            if (fullTimeNode.size() > 1 && fullTimeNode.get(1).isArray()) {
                System.out.println("          📈 全场大小球盘口:");
                parseOverUnderOdds(fullTimeNode.get(1), "全场大小球");
            } else {
                System.out.println("          全场大小球盘口数据缺失 (比赛ID: " + matchId + ")");
            }

            // 独赢盘口
            if (fullTimeNode.size() > 2 && fullTimeNode.get(2).isArray() && fullTimeNode.get(2).size() >= 3) {
                System.out.println("          🏆 全场独赢盘口:");
                parseMoneylineOdds(fullTimeNode.get(2), "全场独赢");
            } else {
                System.out.println("          全场独赢盘口数据缺失或格式错误 (比赛ID: " + matchId + ")");
            }
        } catch (Exception e) {
            System.out.println("          解析全场市场失败 (比赛ID: " + matchId + "): " + e.getMessage());
        }
    }

    /**
     * 解析上半场市场数据 [8][1]
     */
    private void parseHalfTimeMarkets(com.fasterxml.jackson.databind.JsonNode halfTimeNode, long matchId) {
        try {
            if (!halfTimeNode.isArray() || halfTimeNode.size() == 0) {
                System.out.println("          上半场市场数据为空或非数组 (比赛ID: " + matchId + ")");
                return;
            }

            // 让球盘口
            if (halfTimeNode.size() > 0 && halfTimeNode.get(0).isArray()) {
                System.out.println("          🎯 上半场让球盘口:");
                parseHandicapOdds(halfTimeNode.get(0), "上半场让球", matchId);
            } else {
                System.out.println("          上半场让球盘口数据缺失 (比赛ID: " + matchId + ")");
            }

            // 大小球盘口
            if (halfTimeNode.size() > 1 && halfTimeNode.get(1).isArray()) {
                System.out.println("          📈 上半场大小球盘口:");
                parseOverUnderOdds(halfTimeNode.get(1), "上半场大小球");
            } else {
                System.out.println("          上半场大小球盘口数据缺失 (比赛ID: " + matchId + ")");
            }

            // 独赢盘口
            if (halfTimeNode.size() > 2 && halfTimeNode.get(2).isArray() && halfTimeNode.get(2).size() >= 3) {
                System.out.println("          🏆 上半场独赢盘口:");
                parseMoneylineOdds(halfTimeNode.get(2), "上半场独赢");
            } else {
                System.out.println("          上半场独赢盘口数据缺失或格式错误 (比赛ID: " + matchId + ")");
            }
        } catch (Exception e) {
            System.out.println("          解析上半场市场失败 (比赛ID: " + matchId + "): " + e.getMessage());
        }
    }

/**
 * 解析让球盘口
 */
private void parseHandicapOdds(com.fasterxml.jackson.databind.JsonNode handicapNode, String marketName, long matchId) {
    try {
        if (handicapNode == null || !handicapNode.isArray() || handicapNode.size() == 0) {
            System.out.println("          [" + marketName + "] 数据为空或非数组 (比赛ID: " + matchId + ")");
            return;
        }

        boolean hasValidData = false;
        // 让球数据是直接的数组格式：[[1.5, -1.5, "1.5", "1.110", "0.699", ...], ...]
        for (com.fasterxml.jackson.databind.JsonNode oddsItem : handicapNode) {
            if (oddsItem.isArray() && oddsItem.size() >= 5) {
                String handicap = oddsItem.get(2).asText("N/A");
                String homeOdds = oddsItem.get(3).asText("N/A");
                String awayOdds = oddsItem.get(4).asText("N/A");

                if (isValidOdds(homeOdds) && isValidOdds(awayOdds)) {
                    System.out.println(String.format("            [%s] %s: 主队 %s | 客队 %s",
                        marketName, handicap, homeOdds, awayOdds));
                    hasValidData = true;
                } else {
                    System.out.println(String.format("            [%s] %s: 赔率无效 (主队: %s, 客队: %s, 比赛ID: %d)",
                        marketName, handicap, homeOdds, awayOdds, matchId));
                }
            }
        }
        if (!hasValidData) {
            System.out.println("          [" + marketName + "] 无有效让球盘口数据 (比赛ID: " + matchId + ")");
        }
    } catch (Exception e) {
        System.out.println("          解析" + marketName + "失败 (比赛ID: " + matchId + "): " + e.getMessage());
        e.printStackTrace();
    }
}

    /**
     * 验证赔率是否有效（过滤掉长数字ID）
     */
    private boolean isValidOdds(String odds) {
        if (odds == null || odds.trim().isEmpty()) {
            System.out.println("          无效赔率: null或空字符串");
            return false;
        }
        if (odds.matches("\\d{8,}")) {
            System.out.println("          无效赔率: 长数字ID (" + odds + ")");
            return false;
        }
        try {
            double value = Double.parseDouble(odds);
            if (value >= 0.1 && value <= 1000.0) {
                return true;
            } else {
                System.out.println("          无效赔率: 数值超出范围 (" + odds + ")");
                return false;
            }
        } catch (NumberFormatException e) {
            System.out.println("          无效赔率格式: " + odds);
            return false;
        }
    }

    /**
     * 解析大小球盘口 - 根据真实数据结构：二层数组 [[数据]]
     */
    private void parseOverUnderOdds(com.fasterxml.jackson.databind.JsonNode ouNode, String marketName) {
        try {
            if (ouNode.isArray() && ouNode.size() > 0) {
                // 大小球是二层数组结构：[[数据1], [数据2], ...]
                for (com.fasterxml.jackson.databind.JsonNode oddsItem : ouNode) {
                    if (oddsItem.isArray() && oddsItem.size() >= 4) {
                        // 根据真实数据结构解析
                        // 格式：["3.0", 3, "1.100", "0.793", 49733768293, 1, 4000, 1]
                        String total = oddsItem.get(0).asText(); // 盘口显示 "3.0"
                        String overOdds = oddsItem.get(2).asText(); // 大球赔率 "1.100"
                        String underOdds = oddsItem.get(3).asText(); // 小球赔率 "0.793"

                        // 过滤异常数据
                        if (isValidOdds(overOdds) && isValidOdds(underOdds) && !total.isEmpty()) {
                            System.out.println(String.format("          [%s] %s: 大球 %s | 小球 %s",
                                marketName, total, overOdds, underOdds));
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("          解析" + marketName + "失败: " + e.getMessage());
            e.printStackTrace();
        }
    }

    /**
     * 解析独赢盘口 - 根据真实数据结构：一层数组 [数据]
     */
    private void parseMoneylineOdds(com.fasterxml.jackson.databind.JsonNode mlNode, String marketName) {
        try {
            if (mlNode.isArray() && mlNode.size() >= 3) {
                // 独赢是一层数组结构：["6.410", "1.471", "4.380", 3191850746, 0, 2000, 1]
                String homeOdds = mlNode.get(0).asText(); // 主胜赔率 "6.410"
                String drawOdds = mlNode.get(1).asText(); // 平局赔率 "1.471"
                String awayOdds = mlNode.get(2).asText(); // 客胜赔率 "4.380"

                // 过滤异常数据
                if (isValidOdds(homeOdds) && isValidOdds(drawOdds) && isValidOdds(awayOdds)) {
                    System.out.println(String.format("          [%s] 主胜 %s | 平局 %s | 客胜 %s",
                        marketName, homeOdds, drawOdds, awayOdds));
                }
            } else {
                // 如果不是预期的一层数组结构，尝试解析嵌套结构
                for (com.fasterxml.jackson.databind.JsonNode firstLevel : mlNode) {
                    if (firstLevel.isArray() && firstLevel.size() >= 3) {
                        String homeOdds = firstLevel.get(0).asText();
                        String drawOdds = firstLevel.get(1).asText();
                        String awayOdds = firstLevel.get(2).asText();

                        if (isValidOdds(homeOdds) && isValidOdds(drawOdds) && isValidOdds(awayOdds)) {
                            System.out.println(String.format("          [%s] 主胜 %s | 平局 %s | 客胜 %s",
                                marketName, homeOdds, drawOdds, awayOdds));
                            break; // 只显示第一个独赢盘口
                        }
                    }
                }
            }
        } catch (Exception e) {
            System.out.println("          解析" + marketName + "失败: " + e.getMessage());
        }
    }



    /**
     * 解析更新赔率数据
     */
    private void parseUpdateOddsData(com.fasterxml.jackson.databind.JsonNode rootNode) {
        try {
            // 更新数据的解析逻辑
            System.out.println("    处理赔率更新数据...");
        } catch (Exception e) {
            System.out.println("解析更新赔率数据失败: " + e.getMessage());
        }
    }

    /**
     * 获取比赛状态描述
     */
    private String getMatchStatus(int status) {
        switch (status) {
            case 0: return "未开始";
            case 1: return "进行中";
            case 2: return "已结束";
            case 3: return "中场休息";
            case 4: return "延期";
            case 5: return "取消";
            case 6: return "待定";
            case 7: return "进行中";  // 根据您的数据，状态7也是进行中
            case 8: return "暂停";
            case 9: return "中断";
            default: return "状态" + status;
        }
    }

    /**
     * 显示比赛信息（旧版本，保留备用）
     */
    private void displayMatchInfo(com.fasterxml.jackson.databind.JsonNode event, int messageCount) {
        try {
            String eventId = event.has("id") ? event.get("id").asText() : "未知";
            String homeTeam = "未知主队";
            String awayTeam = "未知客队";
            String score = "";
            String time = "";

            // 解析队伍信息
            if (event.has("participants")) {
                com.fasterxml.jackson.databind.JsonNode participants = event.get("participants");
                if (participants.isArray() && participants.size() >= 2) {
                    homeTeam = participants.get(0).has("name") ? participants.get(0).get("name").asText() : "主队";
                    awayTeam = participants.get(1).has("name") ? participants.get(1).get("name").asText() : "客队";
                }
            }

            // 解析比分
            if (event.has("scores")) {
                com.fasterxml.jackson.databind.JsonNode scores = event.get("scores");
                if (scores.isArray() && scores.size() >= 2) {
                    String homeScore = scores.get(0).has("value") ? scores.get(0).get("value").asText() : "0";
                    String awayScore = scores.get(1).has("value") ? scores.get(1).get("value").asText() : "0";
                    score = homeScore + " - " + awayScore;
                }
            }

            // 解析时间
            if (event.has("timer")) {
                com.fasterxml.jackson.databind.JsonNode timer = event.get("timer");
                if (timer.has("seconds")) {
                    int seconds = timer.get("seconds").asInt();
                    int minutes = seconds / 60;
                    time = minutes + "'";
                }
            }

            // 显示比赛信息
            System.out.println(String.format("⚽ [%d] %s vs %s %s %s",
                messageCount, homeTeam, awayTeam, score, time));

            // 显示主要赔率（如果有）
            if (event.has("markets")) {
                displayMainOdds(event.get("markets"));
            }

        } catch (Exception e) {
            System.out.println(String.format("📊 [%d] 接收到比赛数据更新", messageCount));
        }
    }

    /**
     * 显示主要赔率
     */
    private void displayMainOdds(com.fasterxml.jackson.databind.JsonNode markets) {
        try {
            for (com.fasterxml.jackson.databind.JsonNode market : markets) {
                if (market.has("type")) {
                    String marketType = market.get("type").asText();

                    // 只显示主要盘口：胜负、让球、大小球
                    if ("1X2".equals(marketType) || "HANDICAP".equals(marketType) || "OVER_UNDER".equals(marketType)) {
                        System.out.print("   " + getMarketTypeName(marketType) + ": ");

                        if (market.has("selections")) {
                            com.fasterxml.jackson.databind.JsonNode selections = market.get("selections");
                            for (com.fasterxml.jackson.databind.JsonNode selection : selections) {
                                if (selection.has("odds") && selection.has("name")) {
                                    String name = selection.get("name").asText();
                                    String odds = selection.get("odds").asText();
                                    System.out.print(name + "(" + odds + ") ");
                                }
                            }
                        }
                        System.out.println();
                    }
                }
            }
        } catch (Exception e) {
            // 赔率解析失败，忽略
        }
    }

    /**
     * 获取盘口类型中文名称
     */
    private String getMarketTypeName(String marketType) {
        switch (marketType) {
            case "1X2": return "胜负";
            case "HANDICAP": return "让球";
            case "OVER_UNDER": return "大小球";
            default: return marketType;
        }
    }

    /**
     * 初始化完整解决方案
     */
    public boolean initialize() {
        try {
            System.out.println("🚀 初始化完整解决方案...");

            // 1. 初始化浏览器服务
            browserService = new BrowserTokenService();
            browserService.initBrowser();
            System.out.println("✅ 浏览器服务初始化完成");

            // 2. 等待用户登录
            System.out.println("📝 请在浏览器中登录，登录完成后程序会自动检测");
            browserService.waitForManualLogin();
            System.out.println("✅ 检测到登录成功");

            // 3. 获取认证信息
            cookies = browserService.getCookiesFromBrowser();
            ulp = cookies.get("_ulp");

            if (ulp == null || ulp.isEmpty()) {
                System.out.println("❌ 未找到ULP参数");
                return false;
            }

            System.out.println("✅ 获取到认证信息");

            // 4. 初始化WebSocket管理器
            wsManager = new SportsWebSocketManager();
            wsManager.setMessageProcessor(this);

            System.out.println("✅ 完整解决方案初始化成功");
            return true;

        } catch (Exception e) {
            logger.error("初始化失败", e);
            return false;
        }
    }

    /**
     * 使用账号密码初始化完整解决方案（自动登录）
     */
    public boolean initializeWithCredentials(String url, String username, String password) {
        try {
            System.out.println("🚀 初始化完整解决方案（自动登录模式）...");

            // 1. 初始化浏览器服务
            browserService = new BrowserTokenService();
            browserService.initBrowser();
            System.out.println("✅ 浏览器服务初始化完成");

            // 2. 导航到登录页面
            System.out.println("🌐 导航到平博网站: " + url);
            browserService.navigateToUrl(url);

            // 3. 自动填写登录表单
            System.out.println("📝 自动填写登录信息...");
            if (browserService.autoLogin(username, password)) {
                System.out.println("✅ 自动登录成功");
            } else {
                System.out.println("❌ 自动登录失败，请手动登录");
                browserService.waitForManualLogin();
                System.out.println("✅ 检测到手动登录成功");
            }

            // 4. 获取认证信息
            cookies = browserService.getCookiesFromBrowser();
            ulp = cookies.get("_ulp");

            if (ulp == null || ulp.isEmpty()) {
                System.out.println("❌ 未找到ULP参数");
                return false;
            }

            System.out.println("✅ 获取到认证信息");

            // 5. 初始化WebSocket管理器
            wsManager = new SportsWebSocketManager();
            wsManager.setMessageProcessor(this);

            System.out.println("✅ 完整解决方案初始化成功");
            return true;

        } catch (Exception e) {
            logger.error("初始化失败", e);
            return false;
        }
    }

    /**
     * 启动监控
     */
    public void startMonitoring() {
        try {
            System.out.println("\n🔗 启动WebSocket监控...");

            // 使用浏览器自动化获取token并连接
            if (connectWithBrowserToken()) {
                System.out.println("✅ WebSocket连接成功，开始监控");

                // 订阅数据
                wsManager.subscribeToOdds();
                System.out.println("✅ 订阅赔率数据成功");

                // 进入交互模式
                enterInteractiveMode();

            } else {
                System.out.println("❌ WebSocket连接失败");
            }

        } catch (Exception e) {
            logger.error("启动监控失败", e);
        }
    }

    /**
     * 使用浏览器token连接
     */
    private boolean connectWithBrowserToken() {
        try {
            String token = browserService.getTokenFromBrowser();
            if (token != null) {
                System.out.println("✅ 获取到最新token");

                // 使用自动重连功能
                boolean connected = wsManager.connectWithAutoToken(cookies, ulp, true);
                if (connected) {
                    System.out.println("✅ 启用自动重连机制");
                }
                return connected;
            } else {
                System.out.println("❌ 获取token失败");
                return false;
            }
        } catch (Exception e) {
            logger.error("使用浏览器token连接失败", e);
            return false;
        }
    }

    /**
     * 监控模式 - 自动显示比赛数据
     */
    private void enterInteractiveMode() {
        Scanner scanner = new Scanner(System.in);

        System.out.println("\n📊 开始实时监控比赛数据...");
        System.out.println("程序将自动显示接收到的比赛信息和赔率数据");
        System.out.println("输入 'quit' 退出程序，输入 'help' 查看更多命令");
        System.out.println("============================================================");

        boolean running = true;
        while (running) {
            System.out.print("命令 (quit/help): ");
            String command = scanner.nextLine().trim().toLowerCase();

            switch (command) {
                case "status":
                    showStatus();
                    break;

                case "stats":
                    showStatistics();
                    break;

                case "refresh":
                    refreshTokenAndReconnect();
                    break;

                case "reconnect":
                    System.out.println("🔄 手动触发重连...");
                    wsManager.triggerReconnect();
                    break;

                case "quit":
                case "exit":
                    running = false;
                    break;

                case "help":
                    System.out.println("\n可用命令:");
                    System.out.println("  status - 查看连接状态");
                    System.out.println("  stats - 查看统计信息");
                    System.out.println("  refresh - 刷新token并重连");
                    System.out.println("  reconnect - 手动触发重连");
                    System.out.println("  quit - 退出程序");
                    System.out.println();
                    break;

                case "":
                    // 空命令，忽略
                    break;

                default:
                    System.out.println("未知命令: " + command + "。输入 'help' 查看帮助。");
            }
        }

        scanner.close();
    }

    /**
     * 刷新token并重连
     */
    private void refreshTokenAndReconnect() {
        try {
            System.out.println("🔄 刷新token并重连...");

            // 断开当前连接
            wsManager.disconnect();

            // 获取新token
            String newToken = browserService.getTokenFromBrowser();
            if (newToken != null) {
                System.out.println("✅ 获取到新token");

                // 重新连接
                if (wsManager.connect(newToken, ulp)) {
                    wsManager.subscribeToOdds();
                    System.out.println("✅ 重连成功");
                    reconnectCount.incrementAndGet();
                } else {
                    System.out.println("❌ 重连失败");
                }
            } else {
                System.out.println("❌ 获取新token失败");
            }

        } catch (Exception e) {
            logger.error("刷新token失败", e);
        }
    }

    /**
     * 显示状态
     */
    private void showStatus() {
        System.out.println("\n📊 系统状态:");
        System.out.println(String.format("  WebSocket连接: %s", wsManager.isConnected() ? "✅ 已连接" : "❌ 未连接"));
        System.out.println(String.format("  浏览器状态: %s", browserService.isLoggedIn() ? "✅ 已登录" : "❌ 未登录"));
        System.out.println(String.format("  正在重连: %s", wsManager.isReconnecting() ? "🔄 是" : "⏸️ 否"));
        System.out.println(String.format("  重连尝试次数: %d", wsManager.getReconnectAttempts()));
        System.out.println(String.format("  已接收消息数: %d", messageCount.get()));
        System.out.println(String.format("  手动重连次数: %d", reconnectCount.get()));
        System.out.println();
    }

    /**
     * 显示统计信息
     */
    private void showStatistics() {
        System.out.println("\n📈 详细统计:");
        System.out.println(String.format("  连接状态: %s", wsManager.isConnected() ? "已连接" : "未连接"));
        System.out.println(String.format("  浏览器登录: %s", browserService.isLoggedIn() ? "已登录" : "未登录"));
        System.out.println(String.format("  重连状态: %s", wsManager.isReconnecting() ? "重连中" : "正常"));
        System.out.println(String.format("  自动重连次数: %d", wsManager.getReconnectAttempts()));
        System.out.println(String.format("  手动重连次数: %d", reconnectCount.get()));
        System.out.println(String.format("  消息总数: %d 条", messageCount.get()));
        System.out.println(String.format("  当前页面: %s", browserService.getCurrentUrl()));
        System.out.println();
    }

    /**
     * 清理资源
     */
    public void cleanup() {
        try {
            System.out.println("🛑 正在清理资源...");

            // 快速断开WebSocket连接
            if (wsManager != null) {
                try {
                    System.out.println("🔄 断开WebSocket连接...");
                    wsManager.disconnect();
                    System.out.println("✅ WebSocket已断开");
                } catch (Exception e) {
                    logger.warn("断开WebSocket连接失败: {}", e.getMessage());
                }
                wsManager = null;
            }

            // 关闭浏览器（可能耗时）
            if (browserService != null) {
                try {
                    System.out.println("🔄 关闭浏览器...");

                    // 使用超时机制关闭浏览器
                    Thread browserCloseThread = new Thread(() -> {
                        browserService.closeBrowser();
                    });

                    browserCloseThread.start();

                    // 最多等待2秒关闭浏览器
                    try {
                        browserCloseThread.join(2000);
                        if (browserCloseThread.isAlive()) {
                            System.out.println("⚠️ 浏览器关闭超时，继续退出");
                            browserCloseThread.interrupt();
                        }
                    } catch (InterruptedException e) {
                        Thread.currentThread().interrupt();
                    }

                    System.out.println("✅ 浏览器处理完成");
                } catch (Exception e) {
                    logger.warn("关闭浏览器失败: {}", e.getMessage());
                }
                browserService = null;
            }

            System.out.println("✅ 资源清理完成");

        } catch (Exception e) {
            logger.error("清理资源失败", e);
        }
    }

    public static void main(String[] args) {
        System.out.println("🎯 完整解决方案：浏览器自动化 + 自动重连");
        System.out.println("📋 功能特性:");
        System.out.println("   ✅ 浏览器自动化获取token");
        System.out.println("   ✅ 绕过跨域限制");
        System.out.println("   ✅ 自动重连机制");
        System.out.println("   ✅ Token自动刷新");
        System.out.println("   ✅ 实时数据监控");
        System.out.println("   ✅ 交互式控制");
        System.out.println();

        SportsDataMonitor solution = new SportsDataMonitor();

        try {
            // 初始化
            if (solution.initialize()) {
                // 启动监控
                solution.startMonitoring();
            } else {
                System.out.println("❌ 初始化失败");
            }

        } catch (Exception e) {
            logger.error("程序运行出错", e);
        } finally {
            // 清理资源
            solution.cleanup();
        }

        System.out.println("\n🎉 完整解决方案测试完成！");
        System.out.println("👋 程序结束");
    }
}
