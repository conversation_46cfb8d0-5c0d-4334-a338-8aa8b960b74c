package com.zqsport.websocket;



import org.openqa.selenium.Cookie;
import org.openqa.selenium.JavascriptExecutor;
import org.openqa.selenium.WebDriver;
import org.openqa.selenium.chrome.ChromeDriver;
import org.openqa.selenium.chrome.ChromeOptions;
import org.openqa.selenium.support.ui.WebDriverWait;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.time.Duration;
import java.util.HashMap;
import java.util.Map;
import java.util.Set;

/**
 * 使用浏览器自动化获取token的服务
 */
public class BrowserTokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(BrowserTokenService.class);
    private WebDriver driver;
    private WebDriverWait wait;
    private String cachedToken;
    private long tokenCacheTime;
    
    /**
     * 初始化浏览器
     */
    public void initBrowser() {
        try {
            logger.info("初始化浏览器...");

            // 跳过WebDriverManager，直接使用系统ChromeDriver（避免GitHub连接问题）
            logger.info("跳过在线下载，使用本地ChromeDriver...");
            
            // 配置Chrome选项
            ChromeOptions options = new ChromeOptions();
            options.addArguments("--disable-blink-features=AutomationControlled");
            options.addArguments("--disable-extensions");
            options.addArguments("--no-sandbox");
            options.addArguments("--disable-dev-shm-usage");
            options.addArguments("--disable-gpu");
            options.addArguments("--remote-allow-origins=*");
            
            // 设置用户代理
            options.addArguments("--user-agent=Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36");
            
            // 可选：无头模式（后台运行）
            // options.addArguments("--headless");
            
            try {
                driver = new ChromeDriver(options);
                wait = new WebDriverWait(driver, Duration.ofSeconds(10));

                // 隐藏自动化特征
                ((JavascriptExecutor) driver).executeScript("Object.defineProperty(navigator, 'webdriver', {get: () => undefined})");

                logger.info("浏览器初始化完成");

            } catch (Exception driverException) {
                logger.error("ChromeDriver启动失败: {}", driverException.getMessage());

                // 提供详细的解决方案
                System.err.println("\n❌ ChromeDriver启动失败！");
                System.err.println("\n🔧 解决方案:");
                System.err.println("1. 手动下载ChromeDriver:");
                System.err.println("   - 访问: https://chromedriver.chromium.org/downloads");
                System.err.println("   - 下载与您Chrome版本匹配的ChromeDriver");
                System.err.println("   - 将chromedriver.exe放到系统PATH中");
                System.err.println("\n2. 或者将chromedriver.exe放到项目根目录");
                System.err.println("\n3. 确保Chrome浏览器已安装");

                throw new RuntimeException("ChromeDriver启动失败，请按照上述说明安装ChromeDriver", driverException);
            }

        } catch (Exception e) {
            logger.error("初始化浏览器失败", e);
            throw new RuntimeException("浏览器初始化失败", e);
        }
    }
    
    /**
     * 自动登录并获取token
     */
    public String getTokenWithLogin(String username, String password) {
        try {
            if (driver == null) {
                initBrowser();
            }
            
            logger.info("开始自动登录流程...");
            
            // 步骤1：访问登录页面
            logger.info("访问登录页面...");
            driver.get("https://www.pin975.com/zh-cn/compact/sports/soccer");
            Thread.sleep(2000);
            
            // 步骤2：查找并点击登录按钮
            logger.info("查找登录入口...");
            // 这里需要根据实际页面结构调整选择器
            // driver.findElement(By.className("login-button")).click();
            
            // 步骤3：输入用户名和密码
            logger.info("输入登录信息...");
            // driver.findElement(By.name("username")).sendKeys(username);
            // driver.findElement(By.name("password")).sendKeys(password);
            
            // 步骤4：提交登录表单
            // driver.findElement(By.className("submit-button")).click();
            
            // 等待登录完成
            Thread.sleep(3000);
            
            // 步骤5：获取token
            return getTokenFromBrowser();
            
        } catch (Exception e) {
            logger.error("自动登录失败", e);
            return null;
        }
    }
    
    /**
     * 获取缓存的token（如果还有效）或从浏览器重新获取
     */
    public String getTokenFromBrowser() {
        // 检查缓存的token是否还有效（5分钟内）
        if (cachedToken != null && (System.currentTimeMillis() - tokenCacheTime) < 300000) {
            logger.info("使用缓存的token");
            return cachedToken;
        }

        return getTokenFromBrowserForce();
    }

    /**
     * 强制从浏览器重新获取token
     */
    private String getTokenFromBrowserForce() {
        try {
            // 检查浏览器会话是否有效
            if (driver == null || !isBrowserSessionValid()) {
                if (driver == null) {
                    logger.info("浏览器未初始化，正在初始化...");
                    initBrowser();
                    // 需要手动登录
                    logger.info("请在浏览器中手动登录，然后按回车继续...");
                    driver.get("https://www.pin975.com/zh-cn/compact/sports/soccer");
                    System.in.read(); // 等待用户按回车
                } else {
                    logger.info("浏览器会话失效，重新导航到登录页面...");
                    driver.get("https://www.pin975.com/zh-cn/compact/sports/soccer");
                    // 等待页面加载
                    Thread.sleep(2000);
                }
            } else {
                logger.info("复用现有浏览器会话获取token...");
            }
            
            logger.info("从浏览器获取token...");
            
            // 使用JavaScript调用token API
            String script =
                "var callback = arguments[arguments.length - 1];" +
                "fetch('/member-service/v2/wstoken?locale=zh_CN&_=' + Date.now() + '&withCredentials=true', {" +
                "  method: 'GET'," +
                "  credentials: 'include'," +
                "  headers: {" +
                "    'Accept': 'application/json, text/plain, */*'," +
                "    'Cache-Control': 'no-cache'" +
                "  }" +
                "})" +
                ".then(response => response.json())" +
                ".then(data => callback(data.token))" +
                ".catch(error => callback(null));";

            Object result = ((JavascriptExecutor) driver).executeAsyncScript(script);
            
            if (result != null && !result.toString().isEmpty()) {
                String token = result.toString();
                logger.info("✅ 成功获取token: {}...", token.substring(0, Math.min(30, token.length())));

                // 缓存token
                cachedToken = token;
                tokenCacheTime = System.currentTimeMillis();

                return token;
            } else {
                logger.error("❌ 未能获取token");
                return null;
            }
            
        } catch (Exception e) {
            logger.error("从浏览器获取token失败", e);
            return null;
        }
    }
    
    /**
     * 获取浏览器中的cookies
     */
    public Map<String, String> getCookiesFromBrowser() {
        Map<String, String> cookies = new HashMap<>();
        
        try {
            if (driver == null) {
                logger.warn("浏览器未初始化");
                return cookies;
            }
            
            Set<Cookie> browserCookies = driver.manage().getCookies();
            for (Cookie cookie : browserCookies) {
                cookies.put(cookie.getName(), cookie.getValue());
            }
            
            logger.info("从浏览器获取到 {} 个cookies", cookies.size());
            
        } catch (Exception e) {
            logger.error("获取浏览器cookies失败", e);
        }
        
        return cookies;
    }
    
    /**
     * 检查浏览器会话是否有效
     */
    private boolean isBrowserSessionValid() {
        try {
            if (driver == null) {
                return false;
            }

            // 尝试获取当前URL来测试会话是否有效
            driver.getCurrentUrl();
            return true;

        } catch (org.openqa.selenium.NoSuchSessionException e) {
            logger.warn("浏览器会话已失效");
            return false;
        } catch (Exception e) {
            logger.warn("检查浏览器会话时发生错误: {}", e.getMessage());
            return false;
        }
    }

    /**
     * 检查是否已登录
     */
    public boolean isLoggedIn() {
        try {
            if (driver == null) {
                return false;
            }

            // 检查浏览器会话是否还有效
            try {
                driver.getCurrentUrl(); // 测试会话是否有效
            } catch (org.openqa.selenium.NoSuchSessionException e) {
                logger.warn("浏览器会话已失效，需要重新初始化");
                return false;
            }

            // 检查是否存在登录相关的cookies
            Set<Cookie> cookies = driver.manage().getCookies();
            for (Cookie cookie : cookies) {
                if ("auth".equals(cookie.getName()) && "true".equals(cookie.getValue())) {
                    return true;
                }
                if ("custid".equals(cookie.getName()) && cookie.getValue().contains("id=")) {
                    return true;
                }
            }

            return false;

        } catch (Exception e) {
            logger.error("检查登录状态失败", e);
            return false;
        }
    }
    
    /**
     * 等待用户手动登录
     */
    public void waitForManualLogin() {
        try {
            if (driver == null) {
                initBrowser();
            }

            logger.info("打开登录页面，请手动登录...");
            driver.get("https://www.pin975.com/zh-cn/compact/sports/soccer");

            // 等待登录完成
            logger.info("等待登录完成...");
            int attempts = 0;
            int maxAttempts = 60; // 最多等待60秒

            while (!isLoggedIn() && attempts < maxAttempts) {
                Thread.sleep(1000);
                attempts++;

                // 每10秒检查一次浏览器状态
                if (attempts % 10 == 0) {
                    try {
                        driver.getCurrentUrl(); // 测试会话是否有效
                        logger.debug("等待登录... ({}/{})", attempts, maxAttempts);
                    } catch (org.openqa.selenium.NoSuchSessionException e) {
                        logger.warn("浏览器会话失效，重新初始化...");
                        initBrowser();
                        driver.get("https://www.pin975.com/zh-cn/compact/sports/soccer");
                        attempts = 0; // 重置计数器
                    }
                }
            }

            if (isLoggedIn()) {
                logger.info("✅ 检测到登录成功！");
            } else {
                logger.warn("等待登录超时，请检查浏览器状态");
            }

        } catch (Exception e) {
            logger.error("等待手动登录失败", e);
        }
    }
    
    /**
     * 关闭浏览器
     */
    public void closeBrowser() {
        try {
            if (driver != null) {
                logger.info("关闭浏览器...");

                // 尝试正常关闭
                try {
                    driver.quit();
                } catch (Exception e) {
                    logger.warn("正常关闭浏览器失败，尝试强制关闭: {}", e.getMessage());

                    // 强制关闭所有窗口
                    try {
                        driver.close();
                    } catch (Exception ex) {
                        logger.warn("强制关闭窗口失败: {}", ex.getMessage());
                    }

                    // 最后尝试quit
                    try {
                        driver.quit();
                    } catch (Exception ex) {
                        logger.warn("最终quit失败: {}", ex.getMessage());
                    }
                }

                driver = null;
                logger.info("浏览器已关闭");
            }
        } catch (Exception e) {
            logger.error("关闭浏览器过程中出现异常", e);
            // 即使出错也要设置driver为null
            driver = null;
        }
    }
    
    /**
     * 获取当前页面URL
     */
    public String getCurrentUrl() {
        if (driver != null) {
            return driver.getCurrentUrl();
        }
        return null;
    }

    /**
     * 导航到指定URL
     */
    public void navigateToUrl(String url) {
        if (driver != null) {
            logger.info("导航到: {}", url);
            driver.get(url);

            // 等待页面加载
            try {
                Thread.sleep(3000);
            } catch (InterruptedException e) {
                Thread.currentThread().interrupt();
            }
        }
    }

    /**
     * 自动登录
     */
    public boolean autoLogin(String username, String password) {
        try {
            logger.info("开始自动登录...");

            // 等待登录表单加载
            Thread.sleep(2000);

            // 查找并填写用户名
            try {
                org.openqa.selenium.WebElement usernameField = driver.findElement(
                    org.openqa.selenium.By.id("loginId"));
                usernameField.clear();
                usernameField.sendKeys(username);
                logger.info("用户名填写完成");
            } catch (Exception e) {
                logger.warn("未找到用户名输入框: {}", e.getMessage());
                return false;
            }

            // 查找并填写密码
            try {
                org.openqa.selenium.WebElement passwordField = driver.findElement(
                    org.openqa.selenium.By.id("pass"));
                passwordField.clear();
                passwordField.sendKeys(password);
                logger.info("密码填写完成");
            } catch (Exception e) {
                logger.warn("未找到密码输入框: {}", e.getMessage());
                return false;
            }

            // 点击登录按钮
            try {
                org.openqa.selenium.WebElement loginButton = driver.findElement(
                    org.openqa.selenium.By.cssSelector("button[type='submit'].btn-primary"));
                loginButton.click();
                logger.info("点击登录按钮");
            } catch (Exception e) {
                logger.warn("未找到登录按钮: {}", e.getMessage());
                return false;
            }

            // 等待登录处理
            Thread.sleep(5000);

            // 检查是否登录成功（通过检查URL变化或特定元素）
            String currentUrl = driver.getCurrentUrl();
            if (!currentUrl.contains("login") && !currentUrl.contains("signin")) {
                logger.info("自动登录成功");
                return true;
            } else {
                logger.warn("自动登录可能失败，当前URL: {}", currentUrl);
                return false;
            }

        } catch (Exception e) {
            logger.error("自动登录过程中出错", e);
            return false;
        }
    }
}
