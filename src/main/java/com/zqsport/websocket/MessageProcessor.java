package com.zqsport.websocket;

/**
 * 消息处理器接口
 */
public interface MessageProcessor {
    
    /**
     * 处理UPDATE_ODDS消息
     */
    void processUpdateOddsMessage(String jsonMessage);
    
    /**
     * 处理PING消息
     */
    default void processPingMessage(String jsonMessage) {
        // 默认实现：什么都不做
    }
    
    /**
     * 处理RECEIPT消息
     */
    default void processReceiptMessage(String jsonMessage) {
        // 默认实现：什么都不做
    }
    
    /**
     * 处理ERROR消息
     */
    default void processErrorMessage(String jsonMessage) {
        // 默认实现：什么都不做
    }
    
    /**
     * 处理未知类型消息
     */
    default void processUnknownMessage(String messageType, String jsonMessage) {
        // 默认实现：什么都不做
    }
}
