package com.zqsport.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.handshake.ServerHandshake;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.UUID;

/**
 * Sports WebSocket Client for connecting to pin975.com sports betting WebSocket
 */
public class SportsWebSocketClient extends WebSocketClient {

    private static final Logger logger = LoggerFactory.getLogger(SportsWebSocketClient.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private MessageProcessor messageProcessor;
    private SportsWebSocketManager manager;

    public SportsWebSocketClient(URI serverUri, Map<String, String> headers) {
        super(serverUri, headers);
    }

    public SportsWebSocketClient(URI serverUri, Map<String, String> headers, MessageProcessor messageProcessor) {
        super(serverUri, headers);
        this.messageProcessor = messageProcessor;
    }

    /**
     * 设置WebSocket管理器引用
     */
    public void setManager(SportsWebSocketManager manager) {
        this.manager = manager;
    }

    public void setMessageProcessor(MessageProcessor messageProcessor) {
        this.messageProcessor = messageProcessor;
    }
    
    @Override
    public void onOpen(ServerHandshake handshake) {
        logger.info("WebSocket connection opened");
        logger.info("Status: {}", handshake.getHttpStatus());
        logger.info("Status message: {}", handshake.getHttpStatusMessage());
        
        // Send subscription message for ODDS
        sendSubscriptionMessage();
    }
    
    @Override
    public void onMessage(String message) {
        logger.info("📨 收到消息: {}", message.length() > 100 ?
                    message.substring(0, 100) + "..." : message);

        try {
            // Parse and process the received message
            processMessage(message);
        } catch (Exception e) {
            logger.error("Error processing message: {}", message, e);
        }
    }
    
    @Override
    public void onClose(int code, String reason, boolean remote) {
        logger.info("WebSocket connection closed. Code: {}, Reason: {}, Remote: {}",
                   code, reason, remote);

        // 触发重连机制
        if (manager != null) {
            logger.info("触发自动重连机制...");
            manager.triggerReconnect();
        }
    }
    
    @Override
    public void onError(Exception ex) {
        logger.error("WebSocket error occurred", ex);
    }
    
    /**
     * Send subscription message for ODDS destination
     */
    private void sendSubscriptionMessage() {
        try {
            Map<String, Object> subscriptionMessage = new HashMap<>();
            subscriptionMessage.put("type", "SUBSCRIBE");
            subscriptionMessage.put("destination", "ODDS");
            subscriptionMessage.put("id", UUID.randomUUID().toString());
            
            // Add the body parameters as shown in your example
            Map<String, Object> body = new HashMap<>();
            body.put("sp", 29);
            body.put("lg", "");
            body.put("ev", "");
            body.put("mk", 1);
            body.put("btg", "1");
            body.put("ot", 2);
            body.put("d", "");
            body.put("o", 1);
            body.put("l", 100);
            body.put("v", "");
            body.put("lv", "");
            body.put("me", 0);
            body.put("c", "");
            body.put("cl", 100);
            body.put("ec", "");
            body.put("g", "QQ==");
            body.put("hle", false);
            body.put("ic", false);
            body.put("ice", false);
            body.put("inl", false);
            body.put("lang", "");
            body.put("locale", "zh_CN");
            body.put("more", false);
            body.put("pa", 0);
            body.put("pimo", "0,1,8,39,2,3,6,7,4,5");
            body.put("pn", -1);
            body.put("pv", 1);
            body.put("tm", 0);
            
            subscriptionMessage.put("body", body);
            
            String jsonMessage = objectMapper.writeValueAsString(subscriptionMessage);
            logger.info("Sending subscription message: {}", jsonMessage);
            
            send(jsonMessage);
        } catch (Exception e) {
            logger.error("Error sending subscription message", e);
        }
    }
    
    /**
     * Process received messages
     */
    @SuppressWarnings("unchecked")
    private void processMessage(String message) {
        try {
            Map<String, Object> messageMap = objectMapper.readValue(message, Map.class);
            
            String type = (String) messageMap.get("type");
            if (type != null) {
                // 如果有消息处理器，优先使用
                if (messageProcessor != null) {
                    switch (type) {
                        case "FULL_ODDS":
                        case "UPDATE_ODDS":
                            messageProcessor.processUpdateOddsMessage(message);
                            break;
                        case "PING":
                            messageProcessor.processPingMessage(message);
                            break;
                        case "RECEIPT":
                            messageProcessor.processReceiptMessage(message);
                            break;
                        case "ERROR":
                            messageProcessor.processErrorMessage(message);
                            break;
                        case "CONNECTED":
                            logger.info("✅ WebSocket连接确认: {}", message);
                            break;
                        default:
                            messageProcessor.processUnknownMessage(type, message);
                    }
                } else {
                    // 默认处理逻辑
                    switch (type) {
                        case "MESSAGE":
                            processOddsMessage(messageMap);
                            break;
                        case "UPDATE_ODDS":
                            processOddsUpdate(messageMap);
                            break;
                        case "RECEIPT":
                            logger.info("Subscription receipt received: {}", messageMap);
                            break;
                        case "ERROR":
                            logger.error("Error message received: {}", messageMap);
                            break;
                        default:
                            logger.info("Unknown message type: {}", type);
                    }
                }
            }
        } catch (Exception e) {
            logger.error("Error parsing message", e);
        }
    }
    
    /**
     * Process odds data messages
     */
    private void processOddsMessage(Map<String, Object> message) {
        logger.info("Processing odds message: {}", message);

        // Extract and process odds data here
        Object body = message.get("body");
        if (body != null) {
            logger.info("Odds data: {}", body);
            // Add your odds processing logic here
        }
    }

    /**
     * Process odds update messages
     */
    private void processOddsUpdate(Map<String, Object> message) {
        logger.info("Processing odds update: Event ID: {}", message.get("id"));

        // Extract key information from odds update
        Object body = message.get("body");
        if (body != null) {
            logger.debug("Odds update data: {}", body);
            // Add your odds update processing logic here
        }
    }
    
    public static void main(String[] args) {
        try {
            // WebSocket URL with token and ulp parameters
            String wsUrl = "wss://www.pin975.com/sports-websocket/ws?token=AAAAAAOm1MAAAAGYRvNcW4xZ-OjGVb0OWBYaIhUvIo8Gvs5lFtYNiyqKduxUnElU&ulp=L3gwck1lZkl5VTIxdytKOUptWG14YU1tTko5MnpDK3crbGNIYmRsNjZRcE1HcmVwRG5UeFVyd01JU1pveDMxRWJCOW5iRVFhSkxJSUZpaU5TUk52OHc9PXwzZWRjYjdmMTQ4MzMyY2ZiYjdiMzJjNzgwMTBiYjc2Ng%3D%3D";
            
            URI serverUri = new URI(wsUrl);
            
            // Set up headers (similar to browser headers)
            Map<String, String> headers = new HashMap<>();
            headers.put("Origin", "https://www.pin975.com");
            headers.put("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // Create and connect WebSocket client
            SportsWebSocketClient client = new SportsWebSocketClient(serverUri, headers);
            
            logger.info("Connecting to WebSocket: {}", wsUrl);
            client.connect();
            
            // Keep the application running
            Thread.sleep(60000); // Run for 1 minute
            
            client.close();
            
        } catch (Exception e) {
            logger.error("Error in main method", e);
        }
    }
}
