package com.zqsport.websocket;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqsport.websocket.model.OddsData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.net.URI;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.CountDownLatch;
import java.util.concurrent.TimeUnit;

/**
 * Manager class for handling sports WebSocket connections
 */
public class SportsWebSocketManager {

    private static final Logger logger = LoggerFactory.getLogger(SportsWebSocketManager.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    private final TokenService tokenService = new TokenService();
    private SportsWebSocketClient client;
    private CountDownLatch connectionLatch;
    private MessageProcessor messageProcessor;
    private ReconnectManager reconnectManager;
    private Map<String, String> lastUsedCookies;
    private String lastUsedUlp;

    /**
     * 设置消息处理器
     */
    public void setMessageProcessor(MessageProcessor messageProcessor) {
        this.messageProcessor = messageProcessor;
        if (client != null) {
            client.setMessageProcessor(messageProcessor);
        }
    }

    /**
     * Connect to the sports WebSocket with automatic token fetching
     */
    public boolean connectWithAutoToken(Map<String, String> cookies, String ulp) {
        return connectWithAutoToken(cookies, ulp, true);
    }

    /**
     * Connect to the sports WebSocket with automatic token fetching
     */
    public boolean connectWithAutoToken(Map<String, String> cookies, String ulp, boolean enableAutoReconnect) {
        try {
            // 保存连接参数用于重连
            this.lastUsedCookies = cookies;
            this.lastUsedUlp = ulp;

            logger.info("Fetching fresh token...");
            String token = tokenService.fetchToken(cookies);

            if (token == null) {
                logger.error("Failed to fetch token");
                return false;
            }

            boolean connected = connect(token, ulp);

            // 如果连接成功且启用自动重连，初始化重连管理器
            if (connected && enableAutoReconnect) {
                initializeReconnectManager(cookies, ulp);
            }

            return connected;

        } catch (Exception e) {
            logger.error("Error connecting with auto token", e);
            return false;
        }
    }

    /**
     * Connect to the sports WebSocket with automatic token fetching using sample cookies
     */
    public boolean connectWithAutoToken(String ulp) {
        Map<String, String> sampleCookies = TokenService.createSampleCookies();
        return connectWithAutoToken(sampleCookies, ulp);
    }

    /**
     * 初始化重连管理器
     */
    private void initializeReconnectManager(Map<String, String> cookies, String ulp) {
        if (reconnectManager != null) {
            reconnectManager.stop();
        }

        reconnectManager = new ReconnectManager(this, cookies, ulp);
        reconnectManager.setCallback(new ReconnectManager.ReconnectCallback() {
            @Override
            public void onReconnectStart(int attempt) {
                logger.info("🔄 开始第 {} 次重连尝试...", attempt);
            }

            @Override
            public void onReconnectSuccess(int attempt) {
                logger.info("✅ 第 {} 次重连成功！", attempt);
            }

            @Override
            public void onReconnectFailed(int attempt, Exception error) {
                logger.warn("❌ 第 {} 次重连失败: {}", attempt, error.getMessage());
            }

            @Override
            public void onMaxRetriesReached() {
                logger.error("🚫 已达到最大重连次数，停止重连");
            }
        });

        logger.info("✅ 重连管理器已初始化");
    }

    /**
     * Connect to the sports WebSocket with token and ulp parameters
     */
    public boolean connect(String token, String ulp) {
        try {
            String wsUrl = WebSocketConfig.buildWebSocketUrl(token, ulp);
            URI serverUri = new URI(wsUrl);
            
            // Set up headers
            Map<String, String> headers = new HashMap<>();
            headers.put("Origin", WebSocketConfig.ORIGIN);
            headers.put("User-Agent", WebSocketConfig.USER_AGENT);
            
            connectionLatch = new CountDownLatch(1);
            
            // Create WebSocket client
            client = new SportsWebSocketClient(serverUri, headers, messageProcessor) {
                @Override
                public void onOpen(org.java_websocket.handshake.ServerHandshake handshake) {
                    super.onOpen(handshake);
                    connectionLatch.countDown();
                }

                @Override
                public void onClose(int code, String reason, boolean remote) {
                    super.onClose(code, reason, remote);
                    // 通知重连管理器连接已关闭
                    if (reconnectManager != null) {
                        reconnectManager.handleConnectionClosed(code, reason, remote);
                    }
                }

                @Override
                public void onError(Exception ex) {
                    super.onError(ex);
                    // 通知重连管理器发生错误
                    if (reconnectManager != null) {
                        reconnectManager.handleConnectionError(ex);
                    }
                }
            };
            
            logger.info("Connecting to WebSocket: {}", wsUrl);
            client.connect();
            
            // Wait for connection to be established
            boolean connected = connectionLatch.await(WebSocketConfig.CONNECTION_TIMEOUT, TimeUnit.MILLISECONDS);
            
            if (connected) {
                logger.info("Successfully connected to WebSocket");
                return true;
            } else {
                logger.error("Failed to connect to WebSocket within timeout");
                return false;
            }
            
        } catch (Exception e) {
            logger.error("Error connecting to WebSocket", e);
            return false;
        }
    }
    
    /**
     * Subscribe to odds data with custom parameters
     */
    public void subscribeToOdds(int sportId, String locale) {
        if (client == null || !client.isOpen()) {
            logger.error("WebSocket is not connected");
            return;
        }
        
        try {
            Map<String, Object> subscriptionMessage = createSubscriptionMessage(sportId, locale);
            String jsonMessage = objectMapper.writeValueAsString(subscriptionMessage);
            
            logger.info("Sending subscription message: {}", jsonMessage);
            client.send(jsonMessage);
            
        } catch (Exception e) {
            logger.error("Error sending subscription message", e);
        }
    }
    
    /**
     * Subscribe to odds data with default parameters
     */
    public void subscribeToOdds() {
        subscribeToOdds(WebSocketConfig.SubscriptionParams.SPORT_ID, 
                       WebSocketConfig.SubscriptionParams.LOCALE);
    }
    
    /**
     * Create subscription message with specified parameters
     */
    private Map<String, Object> createSubscriptionMessage(int sportId, String locale) {
        Map<String, Object> subscriptionMessage = new HashMap<>();
        subscriptionMessage.put("type", "SUBSCRIBE");
        subscriptionMessage.put("destination", "ODDS");
        subscriptionMessage.put("id", java.util.UUID.randomUUID().toString());
        
        // Create body with parameters
        Map<String, Object> body = new HashMap<>();
        body.put("sp", sportId);
        body.put("lg", "");
        body.put("ev", "");
        body.put("mk", WebSocketConfig.SubscriptionParams.MARKET_TYPE);
        body.put("btg", WebSocketConfig.SubscriptionParams.BET_TYPE_GROUP);
        body.put("ot", WebSocketConfig.SubscriptionParams.ODDS_TYPE);
        body.put("d", "");
        body.put("o", WebSocketConfig.SubscriptionParams.ORDER);
        body.put("l", WebSocketConfig.SubscriptionParams.LIMIT);
        body.put("v", "");
        body.put("lv", "");
        body.put("me", WebSocketConfig.SubscriptionParams.MATCH_EVENT);
        body.put("c", "");
        body.put("cl", WebSocketConfig.SubscriptionParams.COMPETITION_LIMIT);
        body.put("ec", "");
        body.put("g", WebSocketConfig.SubscriptionParams.GROUP);
        body.put("hle", false);
        body.put("ic", false);
        body.put("ice", false);
        body.put("inl", false);
        body.put("lang", "");
        body.put("locale", locale);
        body.put("more", false);
        body.put("pa", 0);
        body.put("pimo", WebSocketConfig.SubscriptionParams.PIMO);
        body.put("pn", WebSocketConfig.SubscriptionParams.PAGE_NUMBER);
        body.put("pv", WebSocketConfig.SubscriptionParams.PAGE_VERSION);
        body.put("tm", WebSocketConfig.SubscriptionParams.TIME_MODE);
        
        subscriptionMessage.put("body", body);
        return subscriptionMessage;
    }
    
    /**
     * Process received odds data
     */
    public void processOddsData(String jsonMessage) {
        try {
            OddsData oddsData = objectMapper.readValue(jsonMessage, OddsData.class);
            logger.info("Processed odds data: {}", oddsData);
            
            // Add your custom odds processing logic here
            handleOddsUpdate(oddsData);
            
        } catch (Exception e) {
            logger.error("Error processing odds data: {}", jsonMessage, e);
        }
    }
    
    /**
     * Handle odds update - override this method for custom processing
     */
    protected void handleOddsUpdate(OddsData oddsData) {
        // Default implementation - just log the data
        logger.info("Odds update received: {}", oddsData);
        
        if (oddsData.getBody() != null) {
            OddsData.OddsBody body = oddsData.getBody();
            logger.info("Sport ID: {}, Market Type: {}, Odds Type: {}", 
                       body.getSportId(), body.getMarketType(), body.getOddsType());
        }
    }
    
    /**
     * 只关闭WebSocket连接，不停止重连管理器（用于重连时）
     */
    public void closeWebSocketOnly() {
        if (client != null) {
            logger.info("关闭WebSocket连接（保持重连管理器）");
            client.close();
            client = null;
        }
    }

    /**
     * Disconnect from WebSocket
     */
    public void disconnect() {
        // 停止重连管理器
        if (reconnectManager != null) {
            reconnectManager.stop();
            reconnectManager = null;
        }

        if (client != null) {
            logger.info("Disconnecting from WebSocket");
            client.close();
            client = null;
        }
    }
    
    /**
     * Check if WebSocket is connected
     */
    public boolean isConnected() {
        return client != null && client.isOpen();
    }
    
    /**
     * Send a custom message to the WebSocket
     */
    public void sendMessage(String message) {
        if (client != null && client.isOpen()) {
            client.send(message);
        } else {
            logger.error("Cannot send message - WebSocket is not connected");
        }
    }

    /**
     * 手动触发重连
     */
    public void triggerReconnect() {
        if (reconnectManager != null) {
            reconnectManager.triggerReconnect();
        } else {
            logger.warn("重连管理器未初始化，无法触发重连");
        }
    }

    /**
     * 检查是否正在重连
     */
    public boolean isReconnecting() {
        return reconnectManager != null && reconnectManager.isReconnecting();
    }

    /**
     * 获取重连尝试次数
     */
    public int getReconnectAttempts() {
        return reconnectManager != null ? reconnectManager.getRetryAttempts() : 0;
    }

    /**
     * 启用/禁用自动重连
     */
    public void setAutoReconnectEnabled(boolean enabled) {
        if (enabled && reconnectManager == null && lastUsedCookies != null && lastUsedUlp != null) {
            initializeReconnectManager(lastUsedCookies, lastUsedUlp);
        } else if (!enabled && reconnectManager != null) {
            reconnectManager.stop();
            reconnectManager = null;
        }
    }
}
