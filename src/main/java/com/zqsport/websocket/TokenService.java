package com.zqsport.websocket;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.io.BufferedReader;
import java.io.InputStreamReader;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.Map;

/**
 * Service for fetching WebSocket tokens from the API
 */
public class TokenService {
    
    private static final Logger logger = LoggerFactory.getLogger(TokenService.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * Fetch a fresh token from the API using provided cookies
     */
    public String fetchToken(Map<String, String> cookies) {
        try {
            String tokenUrl = buildTokenUrl();
            logger.info("Fetching token from: {}", tokenUrl);
            
            URL url = new URL(tokenUrl);
            HttpURLConnection connection = (HttpURLConnection) url.openConnection();
            
            // Set request method and headers
            connection.setRequestMethod("GET");
            connection.setRequestProperty("Accept", "application/json, text/plain, */*");
            connection.setRequestProperty("Accept-Encoding", "gzip, deflate, br, zstd");
            connection.setRequestProperty("Accept-Language", "zh-CN,zh;q=0.9,en;q=0.8,en-GB;q=0.7,en-US;q=0.6");
            connection.setRequestProperty("Cache-Control", "no-cache");
            connection.setRequestProperty("Pragma", "no-cache");
            connection.setRequestProperty("Priority", "u=1, i");
            connection.setRequestProperty("Referer", "https://www.pin975.com/zh-cn/compact/sports/soccer");
            connection.setRequestProperty("User-Agent", WebSocketConfig.USER_AGENT);

            // 关键的CORS相关头部
            connection.setRequestProperty("Origin", "https://www.pin975.com");
            connection.setRequestProperty("Sec-Ch-Ua", "\"Not)A;Brand\";v=\"8\", \"Chromium\";v=\"138\", \"Microsoft Edge\";v=\"138\"");
            connection.setRequestProperty("Sec-Ch-Ua-Mobile", "?0");
            connection.setRequestProperty("Sec-Ch-Ua-Platform", "\"Windows\"");
            connection.setRequestProperty("Sec-Fetch-Dest", "empty");
            connection.setRequestProperty("Sec-Fetch-Mode", "cors");
            connection.setRequestProperty("Sec-Fetch-Site", "same-origin");
            
            // Set cookies if provided
            if (cookies != null && !cookies.isEmpty()) {
                String cookieHeader = buildCookieHeader(cookies);
                connection.setRequestProperty("Cookie", cookieHeader);
                logger.debug("Using cookies: {}", cookieHeader);
            }
            
            // Set timeouts
            connection.setConnectTimeout(WebSocketConfig.CONNECTION_TIMEOUT);
            connection.setReadTimeout(WebSocketConfig.READ_TIMEOUT);
            
            // Get response
            int responseCode = connection.getResponseCode();
            logger.debug("Token API response code: {}", responseCode);
            
            if (responseCode == HttpURLConnection.HTTP_OK) {
                BufferedReader reader = new BufferedReader(new InputStreamReader(connection.getInputStream()));
                StringBuilder response = new StringBuilder();
                String line;
                
                while ((line = reader.readLine()) != null) {
                    response.append(line);
                }
                reader.close();
                
                String responseBody = response.toString();
                logger.debug("Token API response: {}", responseBody);
                
                // Parse JSON response
                JsonNode jsonNode = objectMapper.readTree(responseBody);
                if (jsonNode.has("token")) {
                    String token = jsonNode.get("token").asText();
                    logger.info("Successfully fetched token: {}...", token.substring(0, Math.min(20, token.length())));
                    return token;
                } else {
                    logger.error("Token not found in response: {}", responseBody);
                    return null;
                }
                
            } else {
                logger.error("Failed to fetch token. HTTP response code: {}", responseCode);
                
                // Try to read error response
                BufferedReader errorReader = new BufferedReader(new InputStreamReader(connection.getErrorStream()));
                StringBuilder errorResponse = new StringBuilder();
                String line;
                
                while ((line = errorReader.readLine()) != null) {
                    errorResponse.append(line);
                }
                errorReader.close();
                
                logger.error("Error response: {}", errorResponse.toString());
                return null;
            }
            
        } catch (Exception e) {
            logger.error("Error fetching token", e);
            return null;
        }
    }
    
    /**
     * Fetch token without cookies (may require authentication)
     */
    public String fetchToken() {
        return fetchToken(null);
    }
    
    /**
     * Build the token API URL with timestamp
     */
    private String buildTokenUrl() {
        long timestamp = System.currentTimeMillis();
        return String.format("%s?locale=%s&_=%d&withCredentials=true", 
                           WebSocketConfig.TOKEN_API_URL, 
                           WebSocketConfig.TOKEN_API_LOCALE, 
                           timestamp);
    }
    
    /**
     * Build cookie header from cookie map
     */
    private String buildCookieHeader(Map<String, String> cookies) {
        StringBuilder cookieHeader = new StringBuilder();
        for (Map.Entry<String, String> entry : cookies.entrySet()) {
            if (cookieHeader.length() > 0) {
                cookieHeader.append("; ");
            }
            cookieHeader.append(entry.getKey()).append("=").append(entry.getValue());
        }
        return cookieHeader.toString();
    }
    
    /**
     * Parse cookie string into map
     */
    public static Map<String, String> parseCookieString(String cookieString) {
        Map<String, String> cookies = new java.util.HashMap<>();
        
        if (cookieString != null && !cookieString.trim().isEmpty()) {
            String[] cookiePairs = cookieString.split(";");
            for (String cookiePair : cookiePairs) {
                String[] parts = cookiePair.trim().split("=", 2);
                if (parts.length == 2) {
                    cookies.put(parts[0].trim(), parts[1].trim());
                }
            }
        }
        
        return cookies;
    }
    
    /**
     * Create a sample cookie map based on the provided example
     */
    public static Map<String, String> createSampleCookies() {
        Map<String, String> cookies = new java.util.HashMap<>();
        
        // Add essential cookies from your example
        cookies.put("JSESSIONID", "923E9AD944ECB8982970567C5AB14785");
        cookies.put("pctag", "84ab9095-53e9-4345-83d4-0f73b459f297");
        cookies.put("_sig", "Icy1NVEJrT1RGbVlUWmhNakZsT0dZell3OkRmaTRWc1o5cTRPcGtpcnhXbzBqQnZIVkE6MTYzNzI3NDQ2NDo3NTM0NTQwMzE6Mi4xMC4wOllwVUxCcGlpQ3c%3D");
        cookies.put("_apt", "YpULBpiiCw");
        cookies.put("VG5L", "MG3");
        cookies.put("skin", "pa");
        cookies.put("PCTR", "1911302426123");
        cookies.put("u", "AAAABAAAAAADptTAAAABmEcbRK0zVQ3fzo-Ce9LEbXty0ZID2vItP93iOzMLqAtyzDsx2g==");
        cookies.put("lcu", "AAAABAAAAAADptTAAAABmEcbRK0zVQ3fzo-Ce9LEbXty0ZID2vItP93iOzMLqAtyzDsx2g==");
        cookies.put("custid", "id=MIA4912&login=202507260920&roundTrip=202507260920&hash=1B92734D624F6634C84EF82A1B76C3A4");
        cookies.put("BrowserSessionId", "ae0fbfac-71fd-4a29-9304-f278b67bf958");
        cookies.put("_og", "QQ==");
        cookies.put("_ulp", "L3gwck1lZkl5VTIxdytKOUptWG14YU1tTko5MnpDK3crbGNIYmRsNjZRcE1HcmVwRG5UeFVyd01JU1pveDMxRWJCOW5iRVFhSkxJSUZpaU5TUk52OHc9PXwzZWRjYjdmMTQ4MzMyY2ZiYjdiMzJjNzgwMTBiYjc2Ng==");
        cookies.put("uoc", "74b53f8675375430b8d9a5207cd6fc93");
        cookies.put("_userDefaultView", "COMPACT");
        cookies.put("SLID", "-794245088");
        cookies.put("auth", "true");
        cookies.put("__prefs", "W251bGwsMiwwLDAsMSxudWxsLGZhbHNlLDAuMDAwMCxmYWxzZSx0cnVlLCJBTEwiLDAsbnVsbCx0cnVlLHRydWUsZmFsc2UsZmFsc2UsbnVsbCxudWxsLHRydWVd");
        cookies.put("_lastView", "eyJtaWE0OTEyIjoiQ09NUEFDVCJ9");
        cookies.put("displayMessPopUp", "true");
        cookies.put("lang", "zh_CN");
        
        return cookies;
    }
}
