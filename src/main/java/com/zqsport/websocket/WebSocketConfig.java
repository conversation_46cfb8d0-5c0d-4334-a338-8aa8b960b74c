package com.zqsport.websocket;

/**
 * Configuration class for WebSocket connection parameters
 */
public class WebSocketConfig {
    
    // WebSocket URL components
    public static final String BASE_WS_URL = "wss://www.pin975.com/sports-websocket/ws";
    public static final String ORIGIN = "https://www.pin975.com";
    
    // Default subscription parameters
    public static final class SubscriptionParams {
        public static final int SPORT_ID = 29; // sp parameter
        public static final int MARKET_TYPE = 1; // mk parameter
        public static final String BET_TYPE_GROUP = "1"; // btg parameter
        public static final int ODDS_TYPE = 2; // ot parameter
        public static final int ORDER = 1; // o parameter
        public static final int LIMIT = 100; // l parameter
        public static final int MATCH_EVENT = 0; // me parameter
        public static final int COMPETITION_LIMIT = 100; // cl parameter
        public static final String GROUP = "QQ=="; // g parameter
        public static final String LOCALE = "zh_CN"; // locale parameter
        public static final String PIMO = "0,1,8,39,2,3,6,7,4,5"; // pimo parameter
        public static final int PAGE_NUMBER = -1; // pn parameter
        public static final int PAGE_VERSION = 1; // pv parameter
        public static final int TIME_MODE = 0; // tm parameter
    }
    
    // User-Agent for browser simulation
    public static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/138.0.0.0 Safari/537.36 Edg/138.0.0.0";

    // Token API configuration
    public static final String TOKEN_API_URL = "https://www.pin975.com/member-service/v2/wstoken";
    public static final String TOKEN_API_LOCALE = "zh_CN";

    // Connection timeouts
    public static final int CONNECTION_TIMEOUT = 10000; // 10 seconds
    public static final int READ_TIMEOUT = 30000; // 30 seconds
    
    /**
     * Build complete WebSocket URL with token and ulp parameters
     */
    public static String buildWebSocketUrl(String token, String ulp) {
        return String.format("%s?token=%s&ulp=%s", BASE_WS_URL, token, ulp);
    }
}
