package com.zqsport.websocket.model;

import java.math.BigDecimal;
import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 让球盘口模型
 */
public class HandicapMarket {
    
    private Double handicap; // 让球数，如 -0.5, 0, +0.25
    private String homeOdds; // 主队赔率
    private String awayOdds; // 客队赔率
    private Integer homeOddsValue; // 主队赔率原始值
    private Integer awayOddsValue; // 客队赔率原始值
    private Long marketId; // 盘口ID
    private Long updateTime; // 更新时间戳
    private String period; // 适用时段 (全场/上半场等)
    private Boolean isLive; // 是否滚球
    
    public HandicapMarket() {}
    
    public HandicapMarket(Double handicap, String homeOdds, String awayOdds, 
                         Integer homeOddsValue, Integer awayOddsValue, 
                         Long marketId, Long updateTime) {
        this.handicap = handicap;
        this.homeOdds = homeOdds;
        this.awayOdds = awayOdds;
        this.homeOddsValue = homeOddsValue;
        this.awayOddsValue = awayOddsValue;
        this.marketId = marketId;
        this.updateTime = updateTime;
    }
    
    // Getters and setters
    public Double getHandicap() {
        return handicap;
    }
    
    public void setHandicap(Double handicap) {
        this.handicap = handicap;
    }
    
    public String getHomeOdds() {
        return homeOdds;
    }
    
    public void setHomeOdds(String homeOdds) {
        this.homeOdds = homeOdds;
    }
    
    public String getAwayOdds() {
        return awayOdds;
    }
    
    public void setAwayOdds(String awayOdds) {
        this.awayOdds = awayOdds;
    }
    
    public Integer getHomeOddsValue() {
        return homeOddsValue;
    }
    
    public void setHomeOddsValue(Integer homeOddsValue) {
        this.homeOddsValue = homeOddsValue;
    }
    
    public Integer getAwayOddsValue() {
        return awayOddsValue;
    }
    
    public void setAwayOddsValue(Integer awayOddsValue) {
        this.awayOddsValue = awayOddsValue;
    }
    
    public Long getMarketId() {
        return marketId;
    }
    
    public void setMarketId(Long marketId) {
        this.marketId = marketId;
    }
    
    public Long getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getPeriod() {
        return period;
    }
    
    public void setPeriod(String period) {
        this.period = period;
    }
    
    public Boolean getIsLive() {
        return isLive;
    }
    
    public void setIsLive(Boolean isLive) {
        this.isLive = isLive;
    }
    
    /**
     * 获取让球显示格式
     */
    public String getHandicapDisplay() {
        if (handicap == null) {
            return "0";
        }
        
        if (handicap == 0) {
            return "0";
        } else if (handicap > 0) {
            return "+" + formatHandicap(handicap);
        } else {
            return formatHandicap(handicap);
        }
    }
    
    /**
     * 格式化让球数
     */
    private String formatHandicap(Double handicap) {
        if (handicap == null) {
            return "0";
        }
        
        // 处理常见的让球格式
        if (handicap == 0.25) {
            return "0.25";
        } else if (handicap == -0.25) {
            return "-0.25";
        } else if (handicap == 0.5) {
            return "0.5";
        } else if (handicap == -0.5) {
            return "-0.5";
        } else if (handicap == 0.75) {
            return "0.75";
        } else if (handicap == -0.75) {
            return "-0.75";
        } else if (handicap % 1 == 0) {
            // 整数
            return String.valueOf(handicap.intValue());
        } else {
            // 其他小数
            return String.valueOf(handicap);
        }
    }
    
    /**
     * 获取更新时间格式化显示
     */
    public String getFormattedUpdateTime() {
        if (updateTime == null) {
            return "未知";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(updateTime), 
                ZoneId.systemDefault()
            );
            return dateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        } catch (Exception e) {
            return "时间错误";
        }
    }
    
    /**
     * 获取盘口类型显示
     */
    public String getMarketTypeDisplay() {
        StringBuilder display = new StringBuilder("让球");
        
        if (period != null) {
            display.append("(").append(period).append(")");
        }
        
        if (isLive != null && isLive) {
            display.append("[滚球]");
        }
        
        return display.toString();
    }
    
    @Override
    public String toString() {
        return String.format("%s %s: 主队 %s | 客队 %s (更新: %s)", 
                           getMarketTypeDisplay(),
                           getHandicapDisplay(), 
                           homeOdds != null ? homeOdds : "N/A",
                           awayOdds != null ? awayOdds : "N/A",
                           getFormattedUpdateTime());
    }
}
