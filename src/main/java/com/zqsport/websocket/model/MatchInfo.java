package com.zqsport.websocket.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.List;
import java.util.Map;

/**
 * 比赛信息模型
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class MatchInfo {
    
    @JsonProperty("id")
    private Long matchId;
    
    @JsonProperty("homeTeam")
    private String homeTeam;
    
    @JsonProperty("awayTeam") 
    private String awayTeam;
    
    @JsonProperty("startTime")
    private Long startTimeTimestamp;
    
    @JsonProperty("matchTime")
    private String matchTime; // 比赛进行时间，如 "45'"
    
    @JsonProperty("period")
    private String period; // 比赛阶段，如 "1H", "2H", "HT"
    
    @JsonProperty("homeScore")
    private Integer homeScore;
    
    @JsonProperty("awayScore")
    private Integer awayScore;
    
    @JsonProperty("status")
    private String status; // 比赛状态
    
    @JsonProperty("league")
    private String league; // 联赛名称
    
    @JsonProperty("sportId")
    private Integer sportId;
    
    // 让球盘口列表
    private List<HandicapMarket> handicapMarkets;
    
    // 大小球盘口列表
    private List<TotalMarket> totalMarkets;
    
    // 胜负盘
    private WinDrawWinMarket winDrawWinMarket;
    
    // Getters and setters
    public Long getMatchId() {
        return matchId;
    }
    
    public void setMatchId(Long matchId) {
        this.matchId = matchId;
    }
    
    public String getHomeTeam() {
        return homeTeam;
    }
    
    public void setHomeTeam(String homeTeam) {
        this.homeTeam = homeTeam;
    }
    
    public String getAwayTeam() {
        return awayTeam;
    }
    
    public void setAwayTeam(String awayTeam) {
        this.awayTeam = awayTeam;
    }
    
    public Long getStartTimeTimestamp() {
        return startTimeTimestamp;
    }
    
    public void setStartTimeTimestamp(Long startTimeTimestamp) {
        this.startTimeTimestamp = startTimeTimestamp;
    }
    
    public String getMatchTime() {
        return matchTime;
    }
    
    public void setMatchTime(String matchTime) {
        this.matchTime = matchTime;
    }
    
    public String getPeriod() {
        return period;
    }
    
    public void setPeriod(String period) {
        this.period = period;
    }
    
    public Integer getHomeScore() {
        return homeScore;
    }
    
    public void setHomeScore(Integer homeScore) {
        this.homeScore = homeScore;
    }
    
    public Integer getAwayScore() {
        return awayScore;
    }
    
    public void setAwayScore(Integer awayScore) {
        this.awayScore = awayScore;
    }
    
    public String getStatus() {
        return status;
    }
    
    public void setStatus(String status) {
        this.status = status;
    }
    
    public String getLeague() {
        return league;
    }
    
    public void setLeague(String league) {
        this.league = league;
    }
    
    public Integer getSportId() {
        return sportId;
    }
    
    public void setSportId(Integer sportId) {
        this.sportId = sportId;
    }
    
    public List<HandicapMarket> getHandicapMarkets() {
        return handicapMarkets;
    }
    
    public void setHandicapMarkets(List<HandicapMarket> handicapMarkets) {
        this.handicapMarkets = handicapMarkets;
    }
    
    public List<TotalMarket> getTotalMarkets() {
        return totalMarkets;
    }
    
    public void setTotalMarkets(List<TotalMarket> totalMarkets) {
        this.totalMarkets = totalMarkets;
    }
    
    public WinDrawWinMarket getWinDrawWinMarket() {
        return winDrawWinMarket;
    }
    
    public void setWinDrawWinMarket(WinDrawWinMarket winDrawWinMarket) {
        this.winDrawWinMarket = winDrawWinMarket;
    }
    
    /**
     * 获取格式化的开始时间
     */
    public String getFormattedStartTime() {
        if (startTimeTimestamp == null) {
            return "未知";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(startTimeTimestamp), 
                ZoneId.systemDefault()
            );
            return dateTime.format(DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        } catch (Exception e) {
            return "时间格式错误";
        }
    }
    
    /**
     * 获取比分显示
     */
    public String getScoreDisplay() {
        if (homeScore == null || awayScore == null) {
            return "0-0";
        }
        return homeScore + "-" + awayScore;
    }
    
    /**
     * 获取比赛状态显示
     */
    public String getMatchStatusDisplay() {
        StringBuilder status = new StringBuilder();
        
        if (period != null) {
            status.append(period);
        }
        
        if (matchTime != null && !matchTime.isEmpty()) {
            if (status.length() > 0) {
                status.append(" ");
            }
            status.append(matchTime);
        }
        
        return status.length() > 0 ? status.toString() : "未开始";
    }
    
    @Override
    public String toString() {
        return String.format("%s vs %s [%s] %s %s - %s", 
                           homeTeam != null ? homeTeam : "主队",
                           awayTeam != null ? awayTeam : "客队", 
                           getScoreDisplay(),
                           getMatchStatusDisplay(),
                           league != null ? league : "未知联赛",
                           getFormattedStartTime());
    }
}
