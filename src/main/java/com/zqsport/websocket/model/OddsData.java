package com.zqsport.websocket.model;

import com.fasterxml.jackson.annotation.JsonIgnoreProperties;
import com.fasterxml.jackson.annotation.JsonProperty;

/**
 * Model class for odds data received from WebSocket
 */
@JsonIgnoreProperties(ignoreUnknown = true)
public class OddsData {
    
    @JsonProperty("id")
    private String id;
    
    @JsonProperty("type")
    private String type;
    
    @JsonProperty("destination")
    private String destination;
    
    @JsonProperty("body")
    private OddsBody body;
    
    // Getters and setters
    public String getId() {
        return id;
    }
    
    public void setId(String id) {
        this.id = id;
    }
    
    public String getType() {
        return type;
    }
    
    public void setType(String type) {
        this.type = type;
    }
    
    public String getDestination() {
        return destination;
    }
    
    public void setDestination(String destination) {
        this.destination = destination;
    }
    
    public OddsBody getBody() {
        return body;
    }
    
    public void setBody(OddsBody body) {
        this.body = body;
    }
    
    @Override
    public String toString() {
        return "OddsData{" +
                "id='" + id + '\'' +
                ", type='" + type + '\'' +
                ", destination='" + destination + '\'' +
                ", body=" + body +
                '}';
    }
    
    /**
     * Inner class for odds body data
     */
    @JsonIgnoreProperties(ignoreUnknown = true)
    public static class OddsBody {
        
        @JsonProperty("sp")
        private Integer sportId;
        
        @JsonProperty("lg")
        private String league;
        
        @JsonProperty("ev")
        private String event;
        
        @JsonProperty("mk")
        private Integer marketType;
        
        @JsonProperty("btg")
        private String betTypeGroup;
        
        @JsonProperty("ot")
        private Integer oddsType;
        
        @JsonProperty("odds")
        private Object odds; // This could be a complex object
        
        @JsonProperty("matches")
        private Object matches; // This could be an array of match data
        
        // Getters and setters
        public Integer getSportId() {
            return sportId;
        }
        
        public void setSportId(Integer sportId) {
            this.sportId = sportId;
        }
        
        public String getLeague() {
            return league;
        }
        
        public void setLeague(String league) {
            this.league = league;
        }
        
        public String getEvent() {
            return event;
        }
        
        public void setEvent(String event) {
            this.event = event;
        }
        
        public Integer getMarketType() {
            return marketType;
        }
        
        public void setMarketType(Integer marketType) {
            this.marketType = marketType;
        }
        
        public String getBetTypeGroup() {
            return betTypeGroup;
        }
        
        public void setBetTypeGroup(String betTypeGroup) {
            this.betTypeGroup = betTypeGroup;
        }
        
        public Integer getOddsType() {
            return oddsType;
        }
        
        public void setOddsType(Integer oddsType) {
            this.oddsType = oddsType;
        }
        
        public Object getOdds() {
            return odds;
        }
        
        public void setOdds(Object odds) {
            this.odds = odds;
        }
        
        public Object getMatches() {
            return matches;
        }
        
        public void setMatches(Object matches) {
            this.matches = matches;
        }
        
        @Override
        public String toString() {
            return "OddsBody{" +
                    "sportId=" + sportId +
                    ", league='" + league + '\'' +
                    ", event='" + event + '\'' +
                    ", marketType=" + marketType +
                    ", betTypeGroup='" + betTypeGroup + '\'' +
                    ", oddsType=" + oddsType +
                    ", odds=" + odds +
                    ", matches=" + matches +
                    '}';
        }
    }
}
