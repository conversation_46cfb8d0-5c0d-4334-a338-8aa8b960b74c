package com.zqsport.websocket.model;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 大小球盘口模型
 */
public class TotalMarket {
    
    private Double total; // 大小球盘口，如 2.5, 3.0, 2.5/3.0
    private String overOdds; // 大球赔率
    private String underOdds; // 小球赔率
    private Integer overOddsValue; // 大球赔率原始值
    private Integer underOddsValue; // 小球赔率原始值
    private Long marketId; // 盘口ID
    private Long updateTime; // 更新时间戳
    private String period; // 适用时段 (全场/上半场等)
    private Boolean isLive; // 是否滚球
    private String totalDisplay; // 盘口显示格式，如 "2.5/3.0"
    
    public TotalMarket() {}
    
    public TotalMarket(Double total, String overOdds, String underOdds,
                      Integer overOddsValue, Integer underOddsValue,
                      Long marketId, Long updateTime) {
        this.total = total;
        this.overOdds = overOdds;
        this.underOdds = underOdds;
        this.overOddsValue = overOddsValue;
        this.underOddsValue = underOddsValue;
        this.marketId = marketId;
        this.updateTime = updateTime;
    }
    
    // Getters and setters
    public Double getTotal() {
        return total;
    }
    
    public void setTotal(Double total) {
        this.total = total;
    }
    
    public String getOverOdds() {
        return overOdds;
    }
    
    public void setOverOdds(String overOdds) {
        this.overOdds = overOdds;
    }
    
    public String getUnderOdds() {
        return underOdds;
    }
    
    public void setUnderOdds(String underOdds) {
        this.underOdds = underOdds;
    }
    
    public Integer getOverOddsValue() {
        return overOddsValue;
    }
    
    public void setOverOddsValue(Integer overOddsValue) {
        this.overOddsValue = overOddsValue;
    }
    
    public Integer getUnderOddsValue() {
        return underOddsValue;
    }
    
    public void setUnderOddsValue(Integer underOddsValue) {
        this.underOddsValue = underOddsValue;
    }
    
    public Long getMarketId() {
        return marketId;
    }
    
    public void setMarketId(Long marketId) {
        this.marketId = marketId;
    }
    
    public Long getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getPeriod() {
        return period;
    }
    
    public void setPeriod(String period) {
        this.period = period;
    }
    
    public Boolean getIsLive() {
        return isLive;
    }
    
    public void setIsLive(Boolean isLive) {
        this.isLive = isLive;
    }
    
    public String getTotalDisplay() {
        return totalDisplay;
    }
    
    public void setTotalDisplay(String totalDisplay) {
        this.totalDisplay = totalDisplay;
    }
    
    /**
     * 获取大小球盘口显示格式
     */
    public String getTotalDisplayFormatted() {
        if (totalDisplay != null && !totalDisplay.isEmpty()) {
            return totalDisplay;
        }
        
        if (total == null) {
            return "未知";
        }
        
        // 格式化大小球盘口
        if (total % 1 == 0) {
            // 整数
            return String.valueOf(total.intValue());
        } else if (total % 0.5 == 0) {
            // 0.5的倍数
            return String.valueOf(total);
        } else {
            // 其他小数，可能是组合盘口
            return String.valueOf(total);
        }
    }
    
    /**
     * 获取更新时间格式化显示
     */
    public String getFormattedUpdateTime() {
        if (updateTime == null) {
            return "未知";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(updateTime), 
                ZoneId.systemDefault()
            );
            return dateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        } catch (Exception e) {
            return "时间错误";
        }
    }
    
    /**
     * 获取盘口类型显示
     */
    public String getMarketTypeDisplay() {
        StringBuilder display = new StringBuilder("大小球");
        
        if (period != null) {
            display.append("(").append(period).append(")");
        }
        
        if (isLive != null && isLive) {
            display.append("[滚球]");
        }
        
        return display.toString();
    }
    
    /**
     * 判断是否为组合盘口（如2.5/3.0）
     */
    public boolean isComboTotal() {
        return totalDisplay != null && totalDisplay.contains("/");
    }
    
    /**
     * 获取盘口风险等级
     */
    public String getRiskLevel() {
        if (total == null) {
            return "未知";
        }
        
        if (total <= 1.5) {
            return "低分";
        } else if (total <= 2.5) {
            return "中低分";
        } else if (total <= 3.5) {
            return "中分";
        } else if (total <= 4.5) {
            return "中高分";
        } else {
            return "高分";
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s %s: 大球 %s | 小球 %s (%s, 更新: %s)", 
                           getMarketTypeDisplay(),
                           getTotalDisplayFormatted(),
                           overOdds != null ? overOdds : "N/A",
                           underOdds != null ? underOdds : "N/A",
                           getRiskLevel(),
                           getFormattedUpdateTime());
    }
}
