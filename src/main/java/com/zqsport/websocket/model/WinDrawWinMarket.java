package com.zqsport.websocket.model;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;

/**
 * 胜负盘模型（1X2）
 */
public class WinDrawWinMarket {
    
    private String homeWinOdds; // 主胜赔率
    private String drawOdds; // 平局赔率
    private String awayWinOdds; // 客胜赔率
    private Integer homeWinOddsValue; // 主胜赔率原始值
    private Integer drawOddsValue; // 平局赔率原始值
    private Integer awayWinOddsValue; // 客胜赔率原始值
    private Long marketId; // 盘口ID
    private Long updateTime; // 更新时间戳
    private String period; // 适用时段 (全场/上半场等)
    private Boolean isLive; // 是否滚球
    
    public WinDrawWinMarket() {}
    
    public WinDrawWinMarket(String homeWinOdds, String drawOdds, String awayWinOdds,
                           Integer homeWinOddsValue, Integer drawOddsValue, Integer awayWinOddsValue,
                           Long marketId, Long updateTime) {
        this.homeWinOdds = homeWinOdds;
        this.drawOdds = drawOdds;
        this.awayWinOdds = awayWinOdds;
        this.homeWinOddsValue = homeWinOddsValue;
        this.drawOddsValue = drawOddsValue;
        this.awayWinOddsValue = awayWinOddsValue;
        this.marketId = marketId;
        this.updateTime = updateTime;
    }
    
    // Getters and setters
    public String getHomeWinOdds() {
        return homeWinOdds;
    }
    
    public void setHomeWinOdds(String homeWinOdds) {
        this.homeWinOdds = homeWinOdds;
    }
    
    public String getDrawOdds() {
        return drawOdds;
    }
    
    public void setDrawOdds(String drawOdds) {
        this.drawOdds = drawOdds;
    }
    
    public String getAwayWinOdds() {
        return awayWinOdds;
    }
    
    public void setAwayWinOdds(String awayWinOdds) {
        this.awayWinOdds = awayWinOdds;
    }
    
    public Integer getHomeWinOddsValue() {
        return homeWinOddsValue;
    }
    
    public void setHomeWinOddsValue(Integer homeWinOddsValue) {
        this.homeWinOddsValue = homeWinOddsValue;
    }
    
    public Integer getDrawOddsValue() {
        return drawOddsValue;
    }
    
    public void setDrawOddsValue(Integer drawOddsValue) {
        this.drawOddsValue = drawOddsValue;
    }
    
    public Integer getAwayWinOddsValue() {
        return awayWinOddsValue;
    }
    
    public void setAwayWinOddsValue(Integer awayWinOddsValue) {
        this.awayWinOddsValue = awayWinOddsValue;
    }
    
    public Long getMarketId() {
        return marketId;
    }
    
    public void setMarketId(Long marketId) {
        this.marketId = marketId;
    }
    
    public Long getUpdateTime() {
        return updateTime;
    }
    
    public void setUpdateTime(Long updateTime) {
        this.updateTime = updateTime;
    }
    
    public String getPeriod() {
        return period;
    }
    
    public void setPeriod(String period) {
        this.period = period;
    }
    
    public Boolean getIsLive() {
        return isLive;
    }
    
    public void setIsLive(Boolean isLive) {
        this.isLive = isLive;
    }
    
    /**
     * 获取更新时间格式化显示
     */
    public String getFormattedUpdateTime() {
        if (updateTime == null) {
            return "未知";
        }
        
        try {
            LocalDateTime dateTime = LocalDateTime.ofInstant(
                Instant.ofEpochMilli(updateTime), 
                ZoneId.systemDefault()
            );
            return dateTime.format(DateTimeFormatter.ofPattern("HH:mm:ss"));
        } catch (Exception e) {
            return "时间错误";
        }
    }
    
    /**
     * 获取盘口类型显示
     */
    public String getMarketTypeDisplay() {
        StringBuilder display = new StringBuilder("胜负盘");
        
        if (period != null) {
            display.append("(").append(period).append(")");
        }
        
        if (isLive != null && isLive) {
            display.append("[滚球]");
        }
        
        return display.toString();
    }
    
    /**
     * 获取最热门选项
     */
    public String getFavoriteOutcome() {
        if (homeWinOdds == null || awayWinOdds == null) {
            return "未知";
        }
        
        try {
            double homeOdds = Double.parseDouble(homeWinOdds);
            double awayOdds = Double.parseDouble(awayWinOdds);
            double drawOddsNum = drawOdds != null ? Double.parseDouble(drawOdds) : Double.MAX_VALUE;
            
            if (homeOdds <= awayOdds && homeOdds <= drawOddsNum) {
                return "主胜";
            } else if (awayOdds <= homeOdds && awayOdds <= drawOddsNum) {
                return "客胜";
            } else {
                return "平局";
            }
        } catch (NumberFormatException e) {
            return "未知";
        }
    }
    
    /**
     * 计算返还率
     */
    public String getPayoutRate() {
        if (homeWinOdds == null || awayWinOdds == null || drawOdds == null) {
            return "未知";
        }
        
        try {
            double homeOdds = Double.parseDouble(homeWinOdds);
            double awayOdds = Double.parseDouble(awayWinOdds);
            double drawOddsNum = Double.parseDouble(drawOdds);
            
            double totalImpliedProbability = (1.0 / homeOdds) + (1.0 / awayOdds) + (1.0 / drawOddsNum);
            double payoutRate = (1.0 / totalImpliedProbability) * 100;
            
            return String.format("%.1f%%", payoutRate);
        } catch (NumberFormatException e) {
            return "计算错误";
        }
    }
    
    @Override
    public String toString() {
        return String.format("%s: 主胜 %s | 平局 %s | 客胜 %s (热门: %s, 返还率: %s, 更新: %s)", 
                           getMarketTypeDisplay(),
                           homeWinOdds != null ? homeWinOdds : "N/A",
                           drawOdds != null ? drawOdds : "N/A",
                           awayWinOdds != null ? awayWinOdds : "N/A",
                           getFavoriteOutcome(),
                           getPayoutRate(),
                           getFormattedUpdateTime());
    }
}
