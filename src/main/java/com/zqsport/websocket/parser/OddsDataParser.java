package com.zqsport.websocket.parser;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.zqsport.websocket.model.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * 赔率数据解析器
 */
public class OddsDataParser {
    
    private static final Logger logger = LoggerFactory.getLogger(OddsDataParser.class);
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    /**
     * 解析UPDATE_ODDS消息
     */
    @SuppressWarnings("unchecked")
    public List<MatchInfo> parseUpdateOddsMessage(String jsonMessage) {
        List<MatchInfo> matches = new ArrayList<>();
        
        try {
            Map<String, Object> message = objectMapper.readValue(jsonMessage, Map.class);
            
            if (!"UPDATE_ODDS".equals(message.get("type"))) {
                return matches;
            }
            
            Map<String, Object> odds = (Map<String, Object>) message.get("odds");
            if (odds == null) {
                return matches;
            }
            
            // 解析比赛列表
            List<List<Object>> matchList = (List<List<Object>>) odds.get("l");
            if (matchList != null) {
                for (List<Object> sportData : matchList) {
                    if (sportData.size() >= 3) {
                        Integer sportId = (Integer) sportData.get(0);
                        String sportName = (String) sportData.get(1);
                        List<List<Object>> leagues = (List<List<Object>>) sportData.get(2);
                        
                        if (leagues != null) {
                            for (List<Object> leagueData : leagues) {
                                parseLeagueMatches(leagueData, sportId, sportName, matches);
                            }
                        }
                    }
                }
            }
            
            // 解析赔率更新
            List<List<Object>> updates = (List<List<Object>>) odds.get("u");
            if (updates != null) {
                parseOddsUpdates(updates, matches);
            }
            
        } catch (Exception e) {
            logger.error("解析UPDATE_ODDS消息失败", e);
        }
        
        return matches;
    }
    
    /**
     * 解析联赛比赛数据
     */
    @SuppressWarnings("unchecked")
    private void parseLeagueMatches(List<Object> leagueData, Integer sportId, String sportName, List<MatchInfo> matches) {
        try {
            if (leagueData.size() >= 4) {
                Integer leagueId = (Integer) leagueData.get(0);
                String leagueName = (String) leagueData.get(1);
                List<List<Object>> matchesData = (List<List<Object>>) leagueData.get(2);
                
                if (matchesData != null) {
                    for (List<Object> matchData : matchesData) {
                        MatchInfo match = parseMatchData(matchData, sportId, sportName, leagueName);
                        if (match != null) {
                            matches.add(match);
                        }
                    }
                }
            }
        } catch (Exception e) {
            logger.error("解析联赛数据失败", e);
        }
    }
    
    /**
     * 解析单场比赛数据
     */
    @SuppressWarnings("unchecked")
    private MatchInfo parseMatchData(List<Object> matchData, Integer sportId, String sportName, String leagueName) {
        try {
            if (matchData.size() < 10) {
                return null;
            }
            
            MatchInfo match = new MatchInfo();
            match.setSportId(sportId);
            match.setLeague(leagueName);
            
            // 基本信息
            match.setMatchId(((Number) matchData.get(0)).longValue());
            match.setHomeTeam((String) matchData.get(1));
            match.setAwayTeam((String) matchData.get(2));
            
            if (matchData.get(4) instanceof Number) {
                match.setStartTimeTimestamp(((Number) matchData.get(4)).longValue());
            }
            
            // 比分信息
            if (matchData.size() > 6 && matchData.get(6) instanceof List) {
                List<Integer> scores = (List<Integer>) matchData.get(6);
                if (scores.size() >= 2) {
                    match.setHomeScore(scores.get(0));
                    match.setAwayScore(scores.get(1));
                }
            }
            
            // 比赛状态
            if (matchData.size() > 13 && matchData.get(13) instanceof String) {
                match.setMatchTime((String) matchData.get(13));
            }
            if (matchData.size() > 14 && matchData.get(14) instanceof String) {
                match.setPeriod((String) matchData.get(14));
            }
            
            // 解析赔率数据
            if (matchData.size() > 7 && matchData.get(7) instanceof Map) {
                Map<String, Object> oddsData = (Map<String, Object>) matchData.get(7);
                parseMatchOdds(match, oddsData);
            }
            
            return match;
            
        } catch (Exception e) {
            logger.error("解析比赛数据失败", e);
            return null;
        }
    }
    
    /**
     * 解析比赛赔率数据
     */
    @SuppressWarnings("unchecked")
    private void parseMatchOdds(MatchInfo match, Map<String, Object> oddsData) {
        try {
            // 解析全场赔率
            if (oddsData.containsKey("0")) {
                Map<String, Object> fullTimeOdds = (Map<String, Object>) oddsData.get("0");
                parseFullTimeOdds(match, fullTimeOdds);
            }
            
            // 解析上半场赔率
            if (oddsData.containsKey("1")) {
                Map<String, Object> firstHalfOdds = (Map<String, Object>) oddsData.get("1");
                parseFirstHalfOdds(match, firstHalfOdds);
            }
            
        } catch (Exception e) {
            logger.error("解析赔率数据失败", e);
        }
    }
    
    /**
     * 解析全场赔率
     */
    @SuppressWarnings("unchecked")
    private void parseFullTimeOdds(MatchInfo match, Map<String, Object> oddsData) {
        List<HandicapMarket> handicapMarkets = new ArrayList<>();
        List<TotalMarket> totalMarkets = new ArrayList<>();
        
        // 解析让球盘 (索引0是让球盘数据)
        if (oddsData.containsKey("0") && oddsData.get("0") instanceof List) {
            List<List<Object>> handicapData = (List<List<Object>>) oddsData.get("0");
            for (List<Object> handicap : handicapData) {
                HandicapMarket market = parseHandicapMarket(handicap, "全场", false);
                if (market != null) {
                    handicapMarkets.add(market);
                }
            }
        }
        
        // 解析大小球盘 (索引1是大小球数据)
        if (oddsData.containsKey("1") && oddsData.get("1") instanceof List) {
            List<List<Object>> totalData = (List<List<Object>>) oddsData.get("1");
            for (List<Object> total : totalData) {
                TotalMarket market = parseTotalMarket(total, "全场", false);
                if (market != null) {
                    totalMarkets.add(market);
                }
            }
        }
        
        // 解析胜负盘 (索引2是胜负盘数据)
        if (oddsData.containsKey("2") && oddsData.get("2") instanceof List) {
            List<Object> winDrawWinData = (List<Object>) oddsData.get("2");
            WinDrawWinMarket market = parseWinDrawWinMarket(winDrawWinData, "全场", false);
            match.setWinDrawWinMarket(market);
        }
        
        match.setHandicapMarkets(handicapMarkets);
        match.setTotalMarkets(totalMarkets);
    }
    
    /**
     * 解析上半场赔率
     */
    @SuppressWarnings("unchecked")
    private void parseFirstHalfOdds(MatchInfo match, Map<String, Object> oddsData) {
        // 类似全场赔率解析，但标记为上半场
        // 这里可以扩展上半场赔率的解析逻辑
    }
    
    /**
     * 解析让球盘口
     */
    private HandicapMarket parseHandicapMarket(List<Object> data, String period, boolean isLive) {
        try {
            if (data.size() < 6) {
                return null;
            }
            
            HandicapMarket market = new HandicapMarket();
            market.setPeriod(period);
            market.setIsLive(isLive);
            
            // 解析让球数
            if (data.get(0) instanceof Number) {
                market.setHandicap(((Number) data.get(0)).doubleValue());
            }
            
            // 解析赔率
            if (data.get(1) instanceof String) {
                market.setHomeOdds((String) data.get(1));
            }
            if (data.get(2) instanceof String) {
                market.setAwayOdds((String) data.get(2));
            }
            
            // 解析盘口ID和更新时间
            if (data.size() > 3 && data.get(3) instanceof Number) {
                market.setMarketId(((Number) data.get(3)).longValue());
            }
            if (data.size() > 5 && data.get(5) instanceof Number) {
                market.setUpdateTime(((Number) data.get(5)).longValue());
            }
            
            return market;
            
        } catch (Exception e) {
            logger.error("解析让球盘口失败", e);
            return null;
        }
    }
    
    /**
     * 解析大小球盘口
     */
    private TotalMarket parseTotalMarket(List<Object> data, String period, boolean isLive) {
        try {
            if (data.size() < 4) {
                return null;
            }
            
            TotalMarket market = new TotalMarket();
            market.setPeriod(period);
            market.setIsLive(isLive);
            
            // 解析大小球数
            if (data.get(0) instanceof String) {
                String totalStr = (String) data.get(0);
                market.setTotalDisplay(totalStr);
                try {
                    market.setTotal(Double.parseDouble(totalStr.split("/")[0]));
                } catch (NumberFormatException e) {
                    // 处理组合盘口
                }
            }
            
            // 解析赔率
            if (data.get(1) instanceof String) {
                market.setOverOdds((String) data.get(1));
            }
            if (data.get(2) instanceof String) {
                market.setUnderOdds((String) data.get(2));
            }
            
            // 解析盘口ID和更新时间
            if (data.size() > 3 && data.get(3) instanceof Number) {
                market.setMarketId(((Number) data.get(3)).longValue());
            }
            if (data.size() > 4 && data.get(4) instanceof Number) {
                market.setUpdateTime(((Number) data.get(4)).longValue());
            }
            
            return market;
            
        } catch (Exception e) {
            logger.error("解析大小球盘口失败", e);
            return null;
        }
    }
    
    /**
     * 解析胜负盘
     */
    private WinDrawWinMarket parseWinDrawWinMarket(List<Object> data, String period, boolean isLive) {
        try {
            if (data.size() < 3) {
                return null;
            }
            
            WinDrawWinMarket market = new WinDrawWinMarket();
            market.setPeriod(period);
            market.setIsLive(isLive);
            
            // 解析赔率
            if (data.get(0) instanceof String) {
                market.setHomeWinOdds((String) data.get(0));
            }
            if (data.get(1) instanceof String) {
                market.setAwayWinOdds((String) data.get(1));
            }
            if (data.get(2) instanceof String) {
                market.setDrawOdds((String) data.get(2));
            }
            
            // 解析盘口ID和更新时间
            if (data.size() > 3 && data.get(3) instanceof Number) {
                market.setMarketId(((Number) data.get(3)).longValue());
            }
            if (data.size() > 4 && data.get(4) instanceof Number) {
                market.setUpdateTime(((Number) data.get(4)).longValue());
            }
            
            return market;
            
        } catch (Exception e) {
            logger.error("解析胜负盘失败", e);
            return null;
        }
    }
    
    /**
     * 解析赔率更新数据
     */
    @SuppressWarnings("unchecked")
    private void parseOddsUpdates(List<List<Object>> updates, List<MatchInfo> matches) {
        // 这里可以实现赔率更新的逻辑
        // 根据更新数据修改现有比赛的赔率信息
    }
}
